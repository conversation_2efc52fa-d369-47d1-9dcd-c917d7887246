package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.entity.DistributionOrder;
import com.tourism.miniprogram.entity.Distributor;
import com.tourism.miniprogram.entity.Order;
import com.tourism.miniprogram.mapper.DistributionOrderMapper;
import com.tourism.miniprogram.service.DistributionOrderService;
import com.tourism.miniprogram.service.DistributorService;
import com.tourism.miniprogram.service.DistributionProductCommissionRateService;
import com.tourism.miniprogram.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 分销订单服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service
public class DistributionOrderServiceImpl extends ServiceImpl<DistributionOrderMapper, DistributionOrder> 
        implements DistributionOrderService {

    @Autowired
    private DistributorService distributorService;

    @Autowired
    private DistributionProductCommissionRateService commissionRateService;

    @Autowired
    private OrderService orderService;

    @Override
    public List<DistributionOrder> getByDistributorId(Integer distributorId) {
        try {
            return baseMapper.selectByDistributorId(distributorId);
        } catch (Exception e) {
            log.error("根据分销员ID获取分销订单列表失败，distributorId: {}", distributorId, e);
            throw new RuntimeException("获取分销订单列表失败");
        }
    }

    @Override
    public DistributionOrder getByOrderId(Integer orderId) {
        try {
            return baseMapper.selectByOrderId(orderId);
        } catch (Exception e) {
            log.error("根据订单ID获取分销订单失败，orderId: {}", orderId, e);
            throw new RuntimeException("获取分销订单失败");
        }
    }

    @Override
    public List<DistributionOrder> getByStatus(String status) {
        try {
            return baseMapper.selectByStatus(status);
        } catch (Exception e) {
            log.error("根据状态获取分销订单列表失败，status: {}", status, e);
            throw new RuntimeException("获取分销订单列表失败");
        }
    }

    @Override
    public List<DistributionOrder> getByDistributorIdAndStatus(Integer distributorId, String status) {
        try {
            return baseMapper.selectByDistributorIdAndStatus(distributorId, status);
        } catch (Exception e) {
            log.error("根据分销员ID和状态获取分销订单列表失败，distributorId: {}, status: {}", 
                    distributorId, status, e);
            throw new RuntimeException("获取分销订单列表失败");
        }
    }

    @Override
    @Transactional
    public DistributionOrder createDistributionOrder(Integer distributorId, Integer orderId, 
                                                    BigDecimal orderAmount, BigDecimal commissionRate, BigDecimal baseRate) {
        try {
            log.info("创建分销订单记录，distributorId: {}, orderId: {}, orderAmount: {}, commissionRate: {}", 
                    distributorId, orderId, orderAmount, commissionRate);

            // 检查订单是否已存在分销记录
            if (hasDistributionRecord(orderId)) {
                log.warn("订单已存在分销记录，orderId: {}", orderId);
                return getByOrderId(orderId);
            }

            // 计算佣金金额
            BigDecimal commissionAmount = calculateCommissionAmount(orderAmount, commissionRate);

            // 创建分销订单记录
            DistributionOrder distributionOrder = new DistributionOrder();
            distributionOrder.setDistributorId(distributorId);
            distributionOrder.setOrderId(orderId);
            distributionOrder.setCommissionRate(commissionRate);
            distributionOrder.setCommissionAmount(commissionAmount);
            distributionOrder.setOrderAmount(orderAmount);
            distributionOrder.setStatus("pending");
            distributionOrder.setBaseRate(baseRate);
            distributionOrder.setAppliedRate(commissionRate);

            boolean success = save(distributionOrder);
            if (success) {
                log.info("分销订单记录创建成功，distributionOrderId: {}, distributorId: {}, orderId: {}",
                        distributionOrder.getId(), distributorId, orderId);

                // 检查并处理用户首单激活逻辑
                processFirstOrderActivation(distributorId, orderId);

                return distributionOrder;
            } else {
                log.error("分销订单记录创建失败，数据库保存失败");
                throw new RuntimeException("分销订单记录创建失败");
            }
        } catch (Exception e) {
            log.error("创建分销订单记录失败，distributorId: {}, orderId: {}", distributorId, orderId, e);
            throw new RuntimeException("创建分销订单记录失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean settleDistributionOrder(Integer distributionOrderId) {
        try {
            log.info("结算分销订单，distributionOrderId: {}", distributionOrderId);

            DistributionOrder distributionOrder = getById(distributionOrderId);
            if (distributionOrder == null) {
                log.error("分销订单不存在，distributionOrderId: {}", distributionOrderId);
                return false;
            }

            if (!"pending".equals(distributionOrder.getStatus())) {
                log.error("分销订单状态不允许结算，distributionOrderId: {}, status: {}", 
                        distributionOrderId, distributionOrder.getStatus());
                return false;
            }

            // 更新分销订单状态
            distributionOrder.setStatus("settled");
            distributionOrder.setSettledAt(LocalDateTime.now());

            boolean success = updateById(distributionOrder);
            if (success) {
                // 更新分销员佣金信息
                updateDistributorCommission(distributionOrder.getDistributorId(), distributionOrder.getCommissionAmount());
                log.info("分销订单结算成功，distributionOrderId: {}", distributionOrderId);
            } else {
                log.error("分销订单结算失败，distributionOrderId: {}", distributionOrderId);
            }

            return success;
        } catch (Exception e) {
            log.error("结算分销订单失败，distributionOrderId: {}", distributionOrderId, e);
            throw new RuntimeException("结算分销订单失败");
        }
    }

    @Override
    @Transactional
    public int batchSettleDistributionOrders(List<Integer> distributionOrderIds) {
        try {
            log.info("批量结算分销订单，distributionOrderIds: {}", distributionOrderIds);

            if (distributionOrderIds == null || distributionOrderIds.isEmpty()) {
                log.warn("分销订单ID列表为空，无法进行批量结算");
                return 0;
            }

            int successCount = 0;
            for (Integer distributionOrderId : distributionOrderIds) {
                try {
                    if (settleDistributionOrder(distributionOrderId)) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("批量结算中单个订单失败，distributionOrderId: {}", distributionOrderId, e);
                }
            }

            log.info("批量结算分销订单完成，总数: {}, 成功数: {}", distributionOrderIds.size(), successCount);
            return successCount;
        } catch (Exception e) {
            log.error("批量结算分销订单失败", e);
            throw new RuntimeException("批量结算分销订单失败");
        }
    }

    @Override
    @Transactional
    public boolean cancelDistributionOrder(Integer distributionOrderId) {
        try {
            log.info("取消分销订单，distributionOrderId: {}", distributionOrderId);

            DistributionOrder distributionOrder = getById(distributionOrderId);
            if (distributionOrder == null) {
                log.error("分销订单不存在，distributionOrderId: {}", distributionOrderId);
                return false;
            }

            distributionOrder.setStatus("canceled");
            boolean success = updateById(distributionOrder);
            
            if (success) {
                log.info("分销订单取消成功，distributionOrderId: {}", distributionOrderId);
            } else {
                log.error("分销订单取消失败，distributionOrderId: {}", distributionOrderId);
            }

            return success;
        } catch (Exception e) {
            log.error("取消分销订单失败，distributionOrderId: {}", distributionOrderId, e);
            throw new RuntimeException("取消分销订单失败");
        }
    }

    @Override
    public BigDecimal getPendingCommissionAmount(Integer distributorId) {
        try {
            return baseMapper.getPendingCommissionAmount(distributorId);
        } catch (Exception e) {
            log.error("获取分销员待结算佣金总额失败，distributorId: {}", distributorId, e);
            throw new RuntimeException("获取待结算佣金总额失败");
        }
    }

    @Override
    public BigDecimal getSettledCommissionAmount(Integer distributorId) {
        try {
            return baseMapper.getSettledCommissionAmount(distributorId);
        } catch (Exception e) {
            log.error("获取分销员已结算佣金总额失败，distributorId: {}", distributorId, e);
            throw new RuntimeException("获取已结算佣金总额失败");
        }
    }

    @Override
    public BigDecimal getTotalCommissionAmount(Integer distributorId) {
        try {
            return baseMapper.getTotalCommissionAmount(distributorId);
        } catch (Exception e) {
            log.error("获取分销员总佣金金额失败，distributorId: {}", distributorId, e);
            throw new RuntimeException("获取总佣金金额失败");
        }
    }

    @Override
    public Integer getOrderCountByDistributorId(Integer distributorId) {
        try {
            return baseMapper.countOrdersByDistributorId(distributorId);
        } catch (Exception e) {
            log.error("获取分销员订单数量失败，distributorId: {}", distributorId, e);
            throw new RuntimeException("获取订单数量失败");
        }
    }

    @Override
    public Integer getTotalDistributionOrderCount() {
        try {
            return baseMapper.countTotalDistributionOrders();
        } catch (Exception e) {
            log.error("获取系统总分销订单数量失败", e);
            throw new RuntimeException("获取总分销订单数量失败");
        }
    }

    @Override
    public BigDecimal getTotalSystemCommissionAmount() {
        try {
            return baseMapper.getTotalSystemCommissionAmount();
        } catch (Exception e) {
            log.error("获取系统总分销佣金金额失败", e);
            throw new RuntimeException("获取系统总分销佣金金额失败");
        }
    }

    @Override
    public boolean hasDistributionRecord(Integer orderId) {
        try {
            return baseMapper.existsByOrderId(orderId);
        } catch (Exception e) {
            log.error("检查订单是否存在分销记录失败，orderId: {}", orderId, e);
            return false;
        }
    }

    @Override
    public BigDecimal calculateCommissionAmount(BigDecimal orderAmount, BigDecimal commissionRate) {
        if (orderAmount == null || commissionRate == null) {
            return BigDecimal.ZERO;
        }
        
        // 佣金金额 = 订单金额 * 佣金比例 / 100
        return orderAmount.multiply(commissionRate).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
    }

    @Override
    @Transactional
    public boolean processOrderDistribution(Integer orderId, Integer userId, BigDecimal orderAmount) {
        try {
            log.info("处理订单分销逻辑，orderId: {}, userId: {}, orderAmount: {}", orderId, userId, orderAmount);

            // 检查订单是否已存在分销记录
            if (hasDistributionRecord(orderId)) {
                log.debug("订单已存在分销记录，跳过处理，orderId: {}", orderId);
                return false;
            }

            // 简化逻辑：订单分销需要手动指定分销员，不自动处理
            log.debug("订单分销需要手动指定分销员，orderId: {}, userId: {}", orderId, userId);
            return false;

        } catch (Exception e) {
            log.error("处理订单分销逻辑失败，orderId: {}, userId: {}", orderId, userId, e);
            return false;
        }
    }

    /**
     * 处理用户首单激活逻辑
     *
     * @param distributorId 分销员ID
     * @param orderId 订单ID
     */
    private void processFirstOrderActivation(Integer distributorId, Integer orderId) {
        try {
            log.info("检查用户首单激活逻辑，distributorId: {}, orderId: {}", distributorId, orderId);

            // 获取订单信息，获取用户ID
            Order order = orderService.getById(orderId);
            if (order == null) {
                log.warn("订单不存在，无法处理首单激活逻辑，orderId: {}", orderId);
                return;
            }

            Integer userId = order.getUserId();
            log.info("订单用户ID: {}", userId);

            // 检查该用户是否为首次下单（检查该用户的订单数量）
            Integer userOrderCount = orderService.getUserOrderCount(userId);
            log.info("用户订单总数: {}", userOrderCount);

            // 如果这是用户的第一笔订单，则更新分销员的today_activated+1
            if (userOrderCount != null && userOrderCount == 1) {
                log.info("检测到用户首单，更新分销员今日激活人数，distributorId: {}, userId: {}", distributorId, userId);

                // 调用DistributorMapper的incrementTodayActivated方法
                int updatedRows = distributorService.incrementTodayActivated(distributorId);

                if (updatedRows > 0) {
                    log.info("分销员今日激活人数更新成功，distributorId: {}, userId: {}", distributorId, userId);
                } else {
                    log.warn("分销员今日激活人数更新失败，distributorId: {}, userId: {}", distributorId, userId);
                }
            } else {
                log.debug("用户非首单，不更新激活人数，distributorId: {}, userId: {}, orderCount: {}",
                        distributorId, userId, userOrderCount);
            }

        } catch (Exception e) {
            log.error("处理用户首单激活逻辑失败，distributorId: {}, orderId: {}", distributorId, orderId, e);
            // 这里不抛出异常，避免影响分销订单的创建
        }
    }

    /**
     * 更新分销员佣金信息
     *
     * @param distributorId 分销员ID
     * @param commissionAmount 佣金金额
     */
    private void updateDistributorCommission(Integer distributorId, BigDecimal commissionAmount) {
        try {
            Distributor distributor = distributorService.getById(distributorId);
            if (distributor != null) {
                BigDecimal newTotalCommission = distributor.getTotalCommission().add(commissionAmount);
                BigDecimal newAvailableCommission = distributor.getAvailableCommission().add(commissionAmount);

                distributorService.updateCommissionInfo(distributorId, newTotalCommission,
                                                      newAvailableCommission, distributor.getUnsettledCommission());

                log.debug("分销员佣金信息更新成功，distributorId: {}, addedCommission: {}",
                        distributorId, commissionAmount);
            }
        } catch (Exception e) {
            log.error("更新分销员佣金信息失败，distributorId: {}, commissionAmount: {}",
                    distributorId, commissionAmount, e);
        }
    }
}
