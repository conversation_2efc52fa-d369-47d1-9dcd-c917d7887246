// API服务模块 - 统一入口
const provinceService = require('./provinceService');
const cityService = require('./cityService');
const scenicService = require('./scenicService');
const carouselService = require('./carouselService');
const lecturerService = require('./lecturerService');
const guidePointService = require('./guidePointService');
const relationService = require('./relationService');
const orderService = require('./orderService');
const productService = require('./productService');
const couponService = require('./couponService');
const userService = require('./userService');
const uploadService = require('./upload');
const productBundleService = require('./productBundleService');
const couponUsageRecordService = require('./couponUsageRecordService');
const reviewService = require('./reviewService');

class ApiService {
  constructor() {
    this.provinceService = provinceService;
    this.cityService = cityService;
    this.scenicService = scenicService;
    this.carouselService = carouselService;
    this.lecturerService = lecturerService;
    this.guidePointService = guidePointService;
    this.relationService = relationService;
    this.orderService = orderService;
    this.productService = productService;
    this.couponService = couponService;
    this.userService = userService;
    this.uploadService = uploadService;
    this.productBundleService = productBundleService;
    this.couponUsageRecordService = couponUsageRecordService;
    this.reviewService = reviewService;

    console.log('ApiService 初始化完成');
    console.log('couponUsageRecordService:', this.couponUsageRecordService);
  }

  // 省份相关API
  async getProvinces() {
    return this.provinceService.getProvinces();
  }

  async getProvinceById(provinceId) {
    return this.provinceService.getProvinceById(provinceId);
  }

  // 城市相关API
  async getCities(provinceId) {
    return this.cityService.getCitiesByProvince(provinceId);
  }

  async getCityById(cityId) {
    return this.cityService.getCityById(cityId);
  }

  // 景区相关API
  async getRecommendScenics(params) {
    return this.scenicService.getRecommendScenics(params);
  }

  async getScenicsByProvince(provinceId, params) {
    return this.scenicService.getScenicsByProvince(provinceId, params);
  }

  async getScenicsByCity(cityId, params) {
    return this.scenicService.getScenicsByCity(cityId, params);
  }

  async getScenicDetail(scenicId) {
    return this.scenicService.getScenicDetail(scenicId);
  }

  async searchScenics(keyword, params) {
    return this.scenicService.searchScenics(keyword, params);
  }

  async searchScenicsByPage(params) {
    return this.scenicService.searchScenicsByPage(params);
  }

  async getCommentaryProducts(scenicId, params) {
    return this.scenicService.getCommentaryProducts(scenicId, params);
  }

  async getGuideProductDetail(productId) {
    return this.scenicService.getGuideProductDetail(productId);
  }

  async getProductReviews(productId, params) {
    return this.scenicService.getProductReviews(productId, params);
  }

  // 轮播图相关API
  async getCarousels(provinceId, type) {
    return this.carouselService.getCarousels(provinceId, type);
  }

  async getHomeCarousels(provinceId) {
    return this.carouselService.getHomeCarousels(provinceId);
  }

  async getCarouselsByProvince(provinceId, type) {
    return this.carouselService.getCarouselsByProvince(provinceId, type);
  }

  async getShouyeCarousels() {
    return this.carouselService.getShouyeCarousels();
  }

  async getCarouselDetail(carouselId) {
    return this.carouselService.getCarouselDetail(carouselId);
  }

  // 讲师相关API
  async getLecturerDetail(lecturerId) {
    return this.lecturerService.getLecturerDetail(lecturerId);
  }

  async getLecturerList(params) {
    return this.lecturerService.getLecturerList(params);
  }

  // 指南点相关API
  async getGuidePointList(params) {
    return this.guidePointService.getGuidePointList(params);
  }

  async getGuidePointDetail(pointId) {
    return this.guidePointService.getGuidePointDetail(pointId);
  }

  // 关系服务相关API
  async getProductAreaDetails(productId) {
    return this.relationService.getProductAreaDetails(productId);
  }

  async getAreaPointDetails(areaId) {
    return this.relationService.getAreaPointDetails(areaId);
  }

  async getPointAudioDetails(pointId) {
    return this.relationService.getPointAudioDetails(pointId);
  }

  async getCompleteHierarchyData(productId) {
    return this.relationService.getCompleteHierarchyData(productId);
  }

  async getAreaAudioData(areaId) {
    return this.relationService.getAreaAudioData(areaId);
  }

  // 批量请求
  async batchRequest(requests) {
    try {
      const results = await Promise.allSettled(requests);
      return results.map((result, index) => {
        if (result.status === 'fulfilled') {
          return { success: true, data: result.value, index };
        } else {
          console.error(`批量请求第${index + 1}个失败:`, result.reason);
          return { success: false, error: result.reason, index };
        }
      });
    } catch (error) {
      console.error('批量请求失败:', error);
      throw error;
    }
  }

  // 错误处理
  handleError(error, showToast = true) {
    console.error('API请求错误:', error);

    if (showToast) {
      wx.showToast({
        title: error.message || '请求失败',
        icon: 'none',
        duration: 2000
      });
    }

    return error;
  }

  // 订单相关API
  async createOrder(orderData) {
    return this.orderService.createOrder(orderData);
  }

  async updateOrderStatus(orderId, status) {
    return this.orderService.updateOrderStatus(orderId, status);
  }

  async refundOrder(orderId) {
    return this.orderService.refundOrder(orderId);
  }

  async getOrderDetail(orderId) {
    return this.orderService.getOrderDetail(orderId);
  }

  async getUserOrders(userId, params) {
    return this.orderService.getUserOrders(userId, params);
  }

  async processPayment(orderId, paymentData) {
    return this.orderService.processPayment(orderId, paymentData);
  }

  // 产品相关API
  async createProduct(productData) {
    return this.productService.createProduct(productData);
  }

  async getProductDetail(productId) {
    return this.productService.getProductDetail(productId);
  }

  async updateProduct(productId, updateData) {
    return this.productService.updateProduct(productId, updateData);
  }

  async getProductList(params) {
    return this.productService.getProductList(params);
  }

  async deleteProduct(productId) {
    return this.productService.deleteProduct(productId);
  }

  async getProductsByOrderId(orderId) {
    return this.productService.getProductsByOrderId(orderId);
  }

  // 门票相关API
  async createCoupon(couponData) {
    return this.couponService.createCoupon(couponData);
  }

  async createMultipleCoupons(orderData, quantity) {
    return this.couponService.createMultipleCoupons(orderData, quantity);
  }

  async getUserCoupons(userId, params) {
    return this.couponService.getUserCoupons(userId, params);
  }

  async getCouponDetail(couponId) {
    return this.couponService.getCouponDetail(couponId);
  }

  async activateCoupon(couponId, activationData) {
    return this.couponService.activateCoupon(couponId, activationData);
  }

  async validateCouponCode(code) {
    return this.couponService.validateCouponCode(code);
  }

  async getOrderCoupons(orderId) {
    return this.couponService.getOrderCoupons(orderId);
  }

  // 卡券使用记录相关API
  async createCouponUsageRecords(requestData) {
    return this.couponUsageRecordService.createCouponUsageRecords(requestData);
  }

  async getCouponUsageRecords(couponId) {
    return this.couponUsageRecordService.getCouponUsageRecords(couponId);
  }

  async markCouponAsUsed(couponId, scenicId) {
    return this.couponUsageRecordService.markCouponAsUsed(couponId, scenicId);
  }

  async getCouponUsageStats(couponId) {
    return this.couponUsageRecordService.getCouponUsageStats(couponId);
  }

  // 卡券分享相关API
  async getShareCouponInfo(code) {
    return this.couponService.getShareCouponInfo(code);
  }

  async claimSharedCoupon(code, userId) {
    return this.couponService.claimSharedCoupon(code, userId);
  }

  // 评价相关API
  async getScenicReviews(scenicId, params) {
    return this.reviewService.getScenicReviews(scenicId, params);
  }

  async getScenicReviewStats(scenicId) {
    return this.reviewService.getScenicReviewStats(scenicId);
  }

  // 分销员佣金比例相关API
  async getEffectiveProductCommissionRate(distributorId, productId) {
    try {
      console.log('获取产品有效佣金比例:', distributorId, productId);
      const result = await httpService.get('/api/distribution/commission-rate/effective/product', {
        distributorId: distributorId.toString(),
        productId: productId.toString()
      }, {
        showLoading: false
      });
      console.log('产品有效佣金比例结果:', result);
      return result;
    } catch (error) {
      console.error('获取产品有效佣金比例失败:', error);
      throw error;
    }
  }

  async getEffectiveBundleCommissionRate(distributorId, bundleId) {
    try {
      console.log('获取组合包有效佣金比例:', distributorId, bundleId);
      const result = await httpService.get('/api/distribution/commission-rate/effective/bundle', {
        distributorId: distributorId.toString(),
        bundleId: bundleId.toString()
      }, {
        showLoading: false
      });
      console.log('组合包有效佣金比例结果:', result);
      return result;
    } catch (error) {
      console.error('获取组合包有效佣金比例失败:', error);
      throw error;
    }
  }

  // 用户相关API
  async checkLoginStatus() {
    return this.userService.checkLoginStatus();
  }

  async wechatLogin() {
    return this.userService.wechatLogin();
  }

  async getUserInfo(userId) {
    return this.userService.getUserInfo(userId);
  }

  async updateUserInfo(userInfo) {
    return this.userService.updateUserInfo(userInfo);
  }

  async logout() {
    return this.userService.logout();
  }

  // 文件上传相关API
  async uploadImage(filePath, options) {
    return this.uploadService.uploadImage(filePath, options);
  }

  async uploadAvatar(filePath) {
    return this.uploadService.uploadAvatar(filePath);
  }

  async chooseAndUploadImage(options) {
    return this.uploadService.chooseAndUploadImage(options);
  }

  async uploadMultipleImages(filePaths, options) {
    return this.uploadService.uploadMultipleImages(filePaths, options);
  }

  async deleteFile(fileUrl) {
    return this.uploadService.deleteFile(fileUrl);
  }

  // 组合包相关API
  async getProductBundleDetail(bundleId) {
    return this.productBundleService.getProductBundleDetail(bundleId);
  }

  async getProductBundleList(params) {
    return this.productBundleService.getProductBundleList(params);
  }

  async getEnabledProductBundles(params) {
    return this.productBundleService.getEnabledProductBundles(params);
  }

  async getProductBundlesByScenicIds(scenicIds) {
    return this.productBundleService.getProductBundlesByScenicIds(scenicIds);
  }

  // 清除所有缓存
  clearAllCache() {
    this.provinceService.clearCache();
    this.cityService.clearCache();
    this.scenicService.clearCache();
    this.carouselService.clearCache();
    this.lecturerService.clearCache();
    this.guidePointService.clearCache();
    this.relationService.clearCache();
    this.orderService.clearCache();
    this.productService.clearCache();
    this.couponService.clearCache();
    this.productBundleService.clearCache();
    this.couponUsageRecordService.clearCache();
    this.reviewService.clearCache();
    console.log('所有缓存已清除');
  }
}

// 创建单例实例
const apiService = new ApiService();

module.exports = apiService;
