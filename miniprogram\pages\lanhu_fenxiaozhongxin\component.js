const distributorService = require('../../utils/distributorService');
const userService = require('../../utils/userService');
const httpService = require('../../utils/httpService');
const scenicService = require('../../utils/scenicService');

Page({
  data: {
    loading: false,
    distributorInfo: null,
    isDistributor: false,
    userInfo: null,
    // 显示数据
    displayData: {
      userName: '',
      avatarUrl: '',
      todayActivated: 0,
      totalCommission: '0.00',
      availableCommission: '0.00',
      withdrawnCommission: '0.00',
      unsettledCommission: '0.00',
      commissionRate: '0%',
      distributorCode: '',
      qrcodeUrl: ''
    }
  },

  onLoad: function (options) {
    console.log('推荐中心页面加载');
    this.initPage();
  },

  onShow: function () {
    // 页面显示时刷新数据
    if (this.data.userInfo) {
      this.loadDistributorData();
      // 同时刷新佣金数据
      this.refreshCommissionData();
    }
  },

  // 初始化页面
  async initPage() {
    try {
      this.setData({ loading: true });

      // 获取当前用户信息
      const userInfo = await this.getCurrentUser();
      if (!userInfo) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }

      this.setData({ userInfo });

      // 直接加载推荐员数据，不进行身份验证
      await this.loadDistributorData();

      // 加载完成后刷新佣金数据
      await this.refreshCommissionData();

    } catch (error) {
      console.error('初始化页面失败:', error);
      wx.showToast({
        title: '页面加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 获取当前用户信息
  async getCurrentUser() {
    try {
      // 先尝试从本地存储获取用户信息
      const storedUserInfo = wx.getStorageSync('userInfo');
      if (storedUserInfo && storedUserInfo.id) {
        return storedUserInfo;
      }

      // 如果没有本地用户信息，使用默认用户ID
      return {
        id: 1,
        name: '用户'
      };
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return {
        id: 1,
        name: '用户'
      };
    }
  },

  // 加载推荐员数据
  async loadDistributorData() {
    try {
      const userId = this.data.userInfo.id;
      console.log('加载推荐员数据，用户ID:', userId);

      // 获取推荐员信息
      const distributorInfo = await distributorService.getDistributorByUserId(userId);

      if (distributorInfo) {
        // 用户是推荐员，更新显示数据
        this.setData({
          distributorInfo,
          isDistributor: true
        });
        this.updateDisplayData(distributorInfo);
      } else {
        // 用户不是推荐员
        this.setData({
          distributorInfo: null,
          isDistributor: false
        });
        this.updateDisplayDataForNonDistributor();
      }

    } catch (error) {
      console.error('加载推荐员数据失败:', error);
      wx.showToast({
        title: '加载数据失败',
        icon: 'none'
      });
    }
  },

  // 更新显示数据（推荐员）
  updateDisplayData(distributorInfo) {
    const globalUserInfo = getApp().globalData.userInfo || {};
    const displayData = {
      userName: globalUserInfo.nickName || globalUserInfo.nickname || '推荐员',
      avatarUrl: globalUserInfo.avatarUrl || '',
      todayActivated: distributorInfo.todayActivated || 0,
      totalCommission: distributorService.formatAmount(distributorInfo.totalCommission),
      availableCommission: distributorService.formatAmount(distributorInfo.availableCommission),
      withdrawnCommission: distributorService.formatAmount(distributorInfo.withdrawnCommission),
      unsettledCommission: distributorService.formatAmount(distributorInfo.unsettledCommission),
      commissionRate: distributorService.formatCommissionRate(distributorInfo.commissionRate),
      distributorCode: distributorInfo.distributorCode || '',
      qrcodeUrl: distributorInfo.qrcodeUrl || ''
    };

    this.setData({
      displayData,
      userInfo: globalUserInfo
    });
    console.log('推荐员显示数据更新:', displayData);
    console.log('用户信息更新:', globalUserInfo);
  },

  // 更新显示数据（非推荐员）
  updateDisplayDataForNonDistributor() {
    const globalUserInfo = getApp().globalData.userInfo || {};
    const displayData = {
      userName: globalUserInfo.nickName || globalUserInfo.nickname || this.data.userInfo?.name || '用户',
      avatarUrl: globalUserInfo.avatarUrl || '',
      todayActivated: 0,
      totalCommission: '0.00',
      availableCommission: '0.00',
      withdrawnCommission: '0.00',
      unsettledCommission: '0.00',
      commissionRate: '0%',
      distributorCode: '',
      qrcodeUrl: ''
    };

    this.setData({
      displayData,
      userInfo: globalUserInfo
    });
    console.log('非推荐员显示数据更新:', displayData);
    console.log('用户信息更新:', globalUserInfo);
  },

  // 提现操作
  async handleWithdraw() {
    try {
      // 获取当前佣金数据
      const availableAmount = parseFloat(this.data.displayData.availableCommission);
      const totalAmount = parseFloat(this.data.displayData.totalCommission);
      const withdrawnAmount = parseFloat(this.data.displayData.withdrawnCommission);

      console.log('提现验证 - 可提现金额:', availableAmount, '总金额:', totalAmount, '已提现金额:', withdrawnAmount);

      // 严格验证提现条件
      if (!this.validateWithdrawalConditions(availableAmount, totalAmount, withdrawnAmount)) {
        return;
      }

      // 显示提现确认对话框
      const result = await this.showWithdrawalConfirmDialog(availableAmount);
      if (!result.confirm) {
        return;
      }

      // 执行提现流程
      await this.processWithdrawal(availableAmount);

    } catch (error) {
      console.error('提现操作失败:', error);
      wx.showToast({
        title: '提现失败，请稍后重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 验证提现条件
  validateWithdrawalConditions(availableAmount, totalAmount, withdrawnAmount) {
    // 检查可提现金额是否大于0
    if (availableAmount <= 0) {
      wx.showToast({
        title: '暂无可提现金额',
        icon: 'none',
        duration: 2000
      });
      return false;
    }

    // 检查可提现金额是否超过总佣金
    if (availableAmount > totalAmount) {
      wx.showToast({
        title: '可提现金额异常，请刷新后重试',
        icon: 'none',
        duration: 2000
      });
      return false;
    }

    // 检查已提现金额 + 可提现金额是否超过总佣金
    if ((withdrawnAmount + availableAmount) > totalAmount) {
      wx.showToast({
        title: '提现金额超出限制',
        icon: 'none',
        duration: 2000
      });
      return false;
    }

    // 检查推荐员信息
    if (!this.data.distributorInfo || !this.data.isDistributor) {
      wx.showToast({
        title: '推荐员信息异常',
        icon: 'none',
        duration: 2000
      });
      return false;
    }

    // 检查用户信息
    if (!this.data.userInfo || !this.data.userInfo.id) {
      wx.showToast({
        title: '用户信息异常，请重新登录',
        icon: 'none',
        duration: 2000
      });
      return false;
    }

    return true;
  },

  // 显示提现确认对话框
  showWithdrawalConfirmDialog(amount) {
    return new Promise((resolve) => {
      wx.showModal({
        title: '确认提现',
        content: `确定要提现 ¥${amount.toFixed(2)} 吗？\n\n提现后资金将在1-3个工作日内到账。`,
        confirmText: '确认提现',
        cancelText: '取消',
        success: (res) => {
          resolve(res);
        },
        fail: () => {
          resolve({ confirm: false });
        }
      });
    });
  },

  // 处理提现流程
  async processWithdrawal(amount) {
    try {
      console.log('开始处理提现流程，金额:', amount);

      wx.showLoading({
        title: '提现处理中...',
        mask: true
      });

      // 1. 预留分账接口调用
      await this.callProfitSharingAPI(amount);

      // 2. 创建提现记录
      const withdrawalRecord = await this.createWithdrawalRecord(amount);

      // 3. 更新推荐订单状态为settled
      await this.updateDistributionOrdersToSettled(amount);

      // 4. 重新计算佣金数据
      const updatedCommissionData = await this.calculateCommissionFromOrders(this.data.userInfo.id);

      // 5. 更新推荐员信息到后端
      await this.updateDistributorCommissionInfo(updatedCommissionData);

      // 6. 刷新页面显示数据
      this.updateCommissionDisplay(updatedCommissionData);

      wx.hideLoading();

      // 显示成功提示
      wx.showModal({
        title: '提现成功',
        content: `提现申请已提交成功！资金将在1-3个工作日内到账，请注意查收。`,
        showCancel: false,
        confirmText: '确定',
        success: () => {
          console.log('提现流程完成');
        }
      });

    } catch (error) {
      wx.hideLoading();
      console.error('提现流程失败:', error);

      wx.showModal({
        title: '提现失败',
        content: error.message || '提现处理失败，请稍后重试',
        showCancel: false,
        confirmText: '确定'
      });

      throw error;
    }
  },

  // 预留分账接口调用
  async callProfitSharingAPI(amount) {
    try {
      console.log('调用分账接口，金额:', amount);

      // TODO: 这里预留微信分账接口调用
      // 目前暂时模拟成功，未来可以集成真实的微信分账API

      const profitSharingData = {
        distributorId: this.data.distributorInfo.id,
        amount: amount,
        description: `推荐员提现 - ${this.data.userInfo.nickname || '推荐员'}`,
        timestamp: new Date().getTime()
      };

      console.log('分账数据:', profitSharingData);

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      console.log('分账接口调用成功（模拟）');
      return {
        success: true,
        transactionNo: this.generateTransactionNo(),
        profitSharingData
      };

    } catch (error) {
      console.error('分账接口调用失败:', error);
      throw new Error('分账处理失败');
    }
  },

  // 创建提现记录
  async createWithdrawalRecord(amount) {
    try {
      console.log('创建提现记录，金额:', amount);
      console.log('推荐员信息:', this.data.distributorInfo);
      console.log('用户信息:', this.data.userInfo);

      const transactionNo = this.generateTransactionNo();

      // 确保使用正确的推荐员ID
      let distributorId = null;
      if (this.data.distributorInfo && this.data.distributorInfo.id) {
        distributorId = this.data.distributorInfo.id;
      } else if (this.data.userInfo && this.data.userInfo.id) {
        distributorId = this.data.userInfo.id;
      }

      if (!distributorId) {
        throw new Error('无法获取推荐员ID');
      }

      console.log('使用推荐员ID:', distributorId);

      // 调用提现申请API，使用查询参数方式
      const response = await httpService.post(`/api/distribution/withdrawal/apply?distributorId=${distributorId}&amount=${amount}`, {}, {
        showLoading: false
      });

      let withdrawalRecord;
      if (response && response.data) {
        withdrawalRecord = response.data;
      } else if (response) {
        withdrawalRecord = response;
      } else {
        throw new Error('提现记录创建失败');
      }

      console.log('提现记录创建成功:', withdrawalRecord);

      // 默认提现成功，立即完成提现记录
      if (withdrawalRecord.id) {
        await this.completeWithdrawalRecord(withdrawalRecord.id, transactionNo);
        withdrawalRecord.status = 'completed';
        withdrawalRecord.transactionNo = transactionNo;
        withdrawalRecord.completedAt = new Date().toISOString();
      }

      return withdrawalRecord;

    } catch (error) {
      console.error('创建提现记录失败:', error);
      throw new Error('提现记录创建失败');
    }
  },

  // 完成提现记录
  async completeWithdrawalRecord(withdrawalId, transactionNo) {
    try {
      console.log('完成提现记录，ID:', withdrawalId, '流水号:', transactionNo);

      const response = await httpService.put(`/api/distribution/withdrawal/${withdrawalId}/complete`, {
        transactionNo: transactionNo
      }, {
        showLoading: false
      });

      console.log('提现记录完成成功:', response);
      return response;

    } catch (error) {
      console.error('完成提现记录失败:', error);
      // 这里不抛出错误，因为记录已经创建，只是状态更新失败
      console.warn('提现记录状态更新失败，但提现流程继续');
    }
  },

  // 推荐订单页面
  goToDistributionOrders() {
    wx.navigateTo({
      url: '/pages/lanhu_fenxiaodingdan/component'
    });
  },

  // 提现明细页面
  goToWithdrawalRecords() {
    wx.navigateTo({
      url: '/pages/lanhu_tixianmingxi/component'
    });
  },

  // 更新推荐订单状态为settled
  async updateDistributionOrdersToSettled(withdrawalAmount) {
    try {
      console.log('更新推荐订单状态为settled，提现金额:', withdrawalAmount);

      // 获取当前推荐员的pending状态订单
      let distributorId = null;
      if (this.data.distributorInfo && this.data.distributorInfo.id) {
        distributorId = this.data.distributorInfo.id;
      } else if (this.data.userInfo && this.data.userInfo.id) {
        distributorId = this.data.userInfo.id;
      }

      if (!distributorId) {
        throw new Error('无法获取推荐员ID');
      }

      console.log('使用推荐员ID:', distributorId);
      const response = await httpService.get(`/api/distribution/order/distributor/${distributorId}`, {}, {
        showLoading: false
      });

      let distributionOrders = [];
      if (response && response.data && Array.isArray(response.data)) {
        distributionOrders = response.data;
      } else if (response && Array.isArray(response)) {
        distributionOrders = response;
      }

      // 筛选出pending状态的订单，按创建时间排序
      const pendingOrders = distributionOrders
        .filter(order => order.status === 'pending')
        .sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));

      console.log('找到pending状态订单:', pendingOrders.length, '个');

      // 按顺序结算订单，直到达到提现金额
      let remainingAmount = withdrawalAmount;
      const settledOrderIds = [];

      for (const order of pendingOrders) {
        if (remainingAmount <= 0) break;

        const orderCommission = parseFloat(order.commissionAmount) || 0;
        if (orderCommission > 0 && orderCommission <= remainingAmount) {
          try {
            // 调用结算API
            await httpService.put(`/api/distribution/order/${order.id}/settle`, {}, {
              showLoading: false
            });

            settledOrderIds.push(order.id);
            remainingAmount -= orderCommission;
            console.log(`订单 ${order.id} 结算成功，佣金: ${orderCommission}，剩余金额: ${remainingAmount}`);
          } catch (error) {
            console.error(`订单 ${order.id} 结算失败:`, error);
          }
        }
      }

      console.log('推荐订单状态更新完成，已结算订单:', settledOrderIds);
      return settledOrderIds;

    } catch (error) {
      console.error('更新推荐订单状态失败:', error);
      // 这里不抛出错误，因为提现记录已经创建
      console.warn('推荐订单状态更新失败，但提现流程继续');
      return [];
    }
  },

  // 更新推荐员佣金信息到后端
  async updateDistributorCommissionInfo(commissionData) {
    try {
      console.log('更新推荐员佣金信息到后端:', commissionData);

      // 获取推荐员ID
      let distributorId = null;
      if (this.data.distributorInfo && this.data.distributorInfo.id) {
        distributorId = this.data.distributorInfo.id;
      } else if (this.data.userInfo && this.data.userInfo.id) {
        distributorId = this.data.userInfo.id;
      }

      if (!distributorId) {
        throw new Error('无法获取推荐员ID');
      }

      // 构建更新数据，保留原有的推荐员信息，只更新佣金相关字段
      const updateData = {
        ...this.data.distributorInfo, // 保留原有信息
        totalCommission: parseFloat(commissionData.totalCommission),
        availableCommission: parseFloat(commissionData.availableCommission),
        withdrawnCommission: parseFloat(commissionData.withdrawnCommission),
        unsettledCommission: parseFloat(commissionData.unsettledCommission),
        updatedAt: new Date().toISOString()
      };

      console.log('发送更新数据:', updateData);

      // 调用更新推荐员信息API
      const response = await httpService.put(`/api/distributor/${distributorId}`, updateData, {
        showLoading: false
      });

      console.log('推荐员信息更新成功:', response);

      // 更新本地推荐员信息
      this.setData({
        distributorInfo: {
          ...this.data.distributorInfo,
          totalCommission: parseFloat(commissionData.totalCommission),
          availableCommission: parseFloat(commissionData.availableCommission),
          withdrawnCommission: parseFloat(commissionData.withdrawnCommission),
          unsettledCommission: parseFloat(commissionData.unsettledCommission)
        }
      });

      return response;

    } catch (error) {
      console.error('更新推荐员佣金信息失败:', error);
      // 这里不抛出错误，因为提现流程已经完成，只是信息同步失败
      console.warn('推荐员信息同步失败，但提现流程已完成');
    }
  },

  // 生成交易流水号
  generateTransactionNo() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    const milliseconds = String(now.getMilliseconds()).padStart(3, '0');

    // 格式：WD + 年月日时分秒毫秒 + 随机数
    const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `WD${year}${month}${day}${hours}${minutes}${seconds}${milliseconds}${randomNum}`;
  },

  // 规则说明页面
  goToRules() {
    wx.navigateTo({
      url: '/pages/lanhu_guizeshuoming/component'
    });
  },

  // 推荐人员二维码页面
  goToQrCode() {
    if (!this.data.distributorInfo || !this.data.distributorInfo.id) {
      wx.showToast({
        title: '推荐员信息不完整',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/distributor_qrcode/component?distributorId=${this.data.distributorInfo.id}`
    });
  },

  // 页面跳转处理（兼容原有的通用跳转）
  goTo(e) {
    console.log('页面跳转事件:', e);

    // 暂时显示提示
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  // 返回上一级页面
  goBack() {
    console.log('返回上一级页面');
    wx.navigateBack({
      delta: 1
    });
  },

  // 刷新佣金数据
  async refreshCommissionData() {
    if (!this.data.userInfo || !this.data.userInfo.id) {
      console.log('用户信息不完整，无法刷新佣金数据');
      return;
    }

    try {
      console.log('开始刷新佣金数据');
      wx.showLoading({
        title: '刷新中...',
        mask: true
      });

      // 获取推荐订单数据并计算佣金
      const commissionData = await this.calculateCommissionFromOrders(this.data.userInfo.id);

      // 更新推荐员信息到后端
      await this.updateDistributorCommissionInfo(commissionData);

      // 更新显示数据
      this.updateCommissionDisplay(commissionData);

      wx.hideLoading();
      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500
      });

    } catch (error) {
      console.error('刷新佣金数据失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '刷新失败',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 从订单数据计算佣金
  async calculateCommissionFromOrders(distributorId) {
    try {
      console.log('开始计算佣金，推荐员ID:', distributorId);

      // 获取推荐订单数据
      const response = await httpService.get(`/api/distribution/order/distributor/${distributorId}`, {}, {
        showLoading: false
      });

      let distributionOrders = [];
      if (response && response.data && Array.isArray(response.data)) {
        distributionOrders = response.data;
      } else if (response && Array.isArray(response)) {
        distributionOrders = response;
      }

      console.log('获取到推荐订单数据:', distributionOrders.length, '条');

      // 处理每个订单的详细信息
      const processedOrders = await this.processDistributionOrdersForCommission(distributionOrders);

      // 计算各种佣金金额
      const commissionData = this.calculateCommissionAmounts(processedOrders);

      console.log('佣金计算结果:', commissionData);
      return commissionData;

    } catch (error) {
      console.error('计算佣金失败:', error);
      throw error;
    }
  },

  // 处理推荐订单数据（用于佣金计算）
  async processDistributionOrdersForCommission(distributionOrders) {
    const processedOrders = [];

    for (const order of distributionOrders) {
      try {
        console.log('处理订单:', order.orderId);

        // 获取卡券信息
        const coupons = await this.getOrderCoupons(order.orderId);

        // 处理卡券信息（获取核销时间）
        let verifyTime = '未核销';
        let orderStatus = 'pending';

        if (coupons && coupons.length > 0) {
          // 检查是否有已使用的卡券
          const usedCoupon = coupons.find(coupon => coupon.usedAt);
          if (usedCoupon) {
            verifyTime = this.formatDateTime(usedCoupon.usedAt);
            orderStatus = 'verified';
          } else {
            // 检查是否有已过期的卡券（已取消订单）
            const expiredCoupon = coupons.find(coupon => coupon.status === 'expired');
            if (expiredCoupon) {
              verifyTime = '已取消';
              orderStatus = 'cancelled';
            } else {
              // 检查是否有激活的卡券（已付款）
              const activeCoupon = coupons.find(coupon => coupon.status === 'active');
              if (activeCoupon) {
                orderStatus = 'paid';
              }
            }
          }
        }

        // 根据推荐订单状态进一步判断
        if (order.status === 'canceled') {
          orderStatus = 'cancelled';
          verifyTime = '已取消';
        } else if (order.status === 'settled') {
          orderStatus = 'verified';
        }

        // 构建处理后的订单数据
        const processedOrder = {
          id: order.id,
          orderId: order.orderId,
          status: orderStatus,
          verifyTime: verifyTime,
          commission: order.commissionAmount || 0,
          originalOrder: order,
          coupons: coupons
        };

        processedOrders.push(processedOrder);
        console.log('订单处理完成:', processedOrder);
      } catch (error) {
        console.error('处理订单失败:', order.orderId, error);
        // 即使处理失败也要添加基本信息
        processedOrders.push({
          id: order.id,
          orderId: order.orderId,
          status: order.status === 'canceled' ? 'cancelled' : 'pending',
          verifyTime: order.status === 'canceled' ? '已取消' : '未知',
          commission: order.commissionAmount || 0,
          originalOrder: order
        });
      }
    }

    return processedOrders;
  },

  // 计算佣金金额
  calculateCommissionAmounts(processedOrders) {
    let totalCommission = 0;      // 累计金额
    let availableCommission = 0;  // 可提现金额
    let withdrawnCommission = 0;  // 已提现金额
    let unsettledCommission = 0;  // 未结算金额

    // 按推荐订单的原始状态进行分类计算
    processedOrders.forEach(order => {
      const commission = parseFloat(order.commission) || 0;
      const originalStatus = order.originalOrder ? order.originalOrder.status : 'pending';

      // 根据推荐订单的原始状态计算各种金额
      switch (originalStatus) {
        case 'pending':
          // 待结算状态：计入累计金额
          totalCommission += commission;

          // 只有已核销的订单才能提现
          if (order.status === 'verified' && order.verifyTime !== '未核销' && order.verifyTime !== '已取消') {
            availableCommission += commission;
          } else if (order.status !== 'cancelled' && order.verifyTime !== '已取消') {
            // 其他pending状态的非取消订单归为未结算
            unsettledCommission += commission;
          }
          // 注意：用户订单已取消的情况（order.status === 'cancelled' 或 order.verifyTime === '已取消'）不计入任何金额
          break;

        case 'settled':
          // 已结算状态：计入累计金额和已提现金额
          totalCommission += commission;
          withdrawnCommission += commission;
          break;

        case 'canceled':
          // 推荐订单已取消状态：不计入任何金额
          break;

        default:
          // 其他状态：计入累计金额和未结算金额
          totalCommission += commission;
          unsettledCommission += commission;
          break;
      }
    });

    console.log('佣金计算详情:', {
      totalCommission: totalCommission.toFixed(2),
      availableCommission: availableCommission.toFixed(2),
      withdrawnCommission: withdrawnCommission.toFixed(2),
      unsettledCommission: unsettledCommission.toFixed(2),
      orderCount: processedOrders.length,
      pendingCount: processedOrders.filter(order => order.originalOrder?.status === 'pending').length,
      settledCount: processedOrders.filter(order => order.originalOrder?.status === 'settled').length,
      cancelledCount: processedOrders.filter(order => order.originalOrder?.status === 'canceled').length,
      userCancelledCount: processedOrders.filter(order => order.status === 'cancelled' || order.verifyTime === '已取消').length
    });

    return {
      totalCommission: totalCommission.toFixed(2),
      availableCommission: availableCommission.toFixed(2),
      withdrawnCommission: withdrawnCommission.toFixed(2),
      unsettledCommission: unsettledCommission.toFixed(2),
      orderCount: processedOrders.length,
      pendingCount: processedOrders.filter(order => order.originalOrder?.status === 'pending').length,
      settledCount: processedOrders.filter(order => order.originalOrder?.status === 'settled').length,
      cancelledCount: processedOrders.filter(order => order.originalOrder?.status === 'canceled').length,
      userCancelledCount: processedOrders.filter(order => order.status === 'cancelled' || order.verifyTime === '已取消').length
    };
  },

  // 更新佣金显示
  updateCommissionDisplay(commissionData) {
    const currentDisplayData = this.data.displayData;
    const updatedDisplayData = {
      ...currentDisplayData,
      totalCommission: commissionData.totalCommission,
      availableCommission: commissionData.availableCommission,
      withdrawnCommission: commissionData.withdrawnCommission,
      unsettledCommission: commissionData.unsettledCommission
    };

    this.setData({
      displayData: updatedDisplayData
    });

    console.log('佣金显示数据已更新:', updatedDisplayData);
  },

  // 获取订单卡券信息
  async getOrderCoupons(orderId) {
    try {
      const response = await httpService.get(`/api/coupons/order/${orderId}`, {}, {
        showLoading: false
      });

      if (response && response.data && Array.isArray(response.data)) {
        return response.data;
      } else if (response && Array.isArray(response)) {
        return response;
      }
      return [];
    } catch (error) {
      console.error('获取订单卡券失败:', orderId, error);
      return [];
    }
  },

  // 格式化日期时间
  formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '';

    try {
      const date = new Date(dateTimeStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}`;
    } catch (error) {
      console.error('日期格式化失败:', error);
      return dateTimeStr;
    }
  }
})
