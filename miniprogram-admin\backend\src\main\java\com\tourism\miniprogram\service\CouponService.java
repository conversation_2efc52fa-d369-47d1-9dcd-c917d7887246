package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.entity.Coupon;

import java.util.List;

/**
 * 数字门票服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
public interface CouponService extends IService<Coupon> {

    /**
     * 根据用户ID获取门票列表
     *
     * @param userId 用户ID
     * @return 门票列表
     */
    List<Coupon> getCouponsByUserId(Integer userId);

    /**
     * 根据景区ID获取门票列表
     *
     * @param scenicId 景区ID
     * @return 门票列表
     */
    List<Coupon> getCouponsByScenicId(String scenicId);

    /**
     * 根据状态获取门票列表
     *
     * @param status 门票状态
     * @return 门票列表
     */
    List<Coupon> getCouponsByStatus(String status);

    /**
     * 根据订单ID获取门票列表
     *
     * @param orderId 订单ID
     * @return 门票列表
     */
    List<Coupon> getCouponsByOrderId(Integer orderId);

    /**
     * 根据用户ID和景区ID获取有效门票
     *
     * @param userId 用户ID
     * @param scenicId 景区ID
     * @return 门票列表
     */
    List<Coupon> getValidCouponsByUserAndScenic(Integer userId, String scenicId);

    /**
     * 激活门票
     *
     * @param couponId 门票ID
     * @param activationCode 激活码
     * @return 是否成功
     */
    boolean activateCoupon(Integer couponId, String activationCode);

    /**
     * 直接激活门票（无需激活码）
     *
     * @param couponId 门票ID
     * @return 是否成功
     */
    boolean activateCouponDirect(Integer couponId);

    /**
     * 使用门票
     *
     * @param couponId 门票ID
     * @param userId 用户ID
     * @param scenicId 景区ID
     * @return 是否成功
     */
    boolean useCoupon(Integer couponId, Integer userId, String scenicId);

    /**
     * 生成门票码
     *
     * @return 门票码
     */
    String generateCouponCode();

    /**
     * 根据用户ID和景区ID查询未激活的数字门票
     *
     * @param userId 用户ID
     * @param scenicId 景区ID
     * @return 未激活的门票列表
     */
    List<Coupon> getUnactivatedCouponsByUserAndScenic(Integer userId, String scenicId);

    /**
     * 检查用户在指定景区是否有未激活的数字门票
     *
     * @param userId 用户ID
     * @param scenicId 景区ID
     * @return 是否存在未激活门票
     */
    boolean hasUnactivatedCoupons(Integer userId, String scenicId);

    /**
     * 根据用户ID和产品ID查询未激活的数字门票
     *
     * @param userId 用户ID
     * @param productId 产品ID
     * @return 未激活的门票列表
     */
    List<Coupon> getUnactivatedCouponsByUserAndProduct(Integer userId, String productId);

    /**
     * 检查用户在指定产品是否有未激活的数字门票
     *
     * @param userId 用户ID
     * @param productId 产品ID
     * @return 是否存在未激活门票
     */
    boolean hasUnactivatedCouponsByProduct(Integer userId, String productId);

    /**
     * 根据用户ID和景区ID查询已激活的数字门票
     *
     * @param userId 用户ID
     * @param scenicId 景区ID
     * @return 已激活的门票列表
     */
    List<Coupon> getActivatedCouponsByUserAndScenic(Integer userId, String scenicId);

    /**
     * 检查用户在指定景区是否有已激活的数字门票
     *
     * @param userId 用户ID
     * @param scenicId 景区ID
     * @return 是否存在已激活门票
     */
    boolean hasActivatedCoupons(Integer userId, String scenicId);

    /**
     * 根据用户ID和产品ID查询已激活的数字门票
     *
     * @param userId 用户ID
     * @param productId 产品ID
     * @return 已激活的门票列表
     */
    List<Coupon> getActivatedCouponsByUserAndProduct(Integer userId, String productId);

    /**
     * 检查用户在指定产品是否有已激活的数字门票
     *
     * @param userId 用户ID
     * @param productId 产品ID
     * @return 是否存在已激活门票
     */
    boolean hasActivatedCouponsByProduct(Integer userId, String productId);

    /**
     * 处理过期卡券，将满足条件的卡券状态更新为'used'
     * 条件：used_at不为null（已被使用）且valid_to小于当前时间（已过期）且status不等于'used'（避免重复更新）
     *
     * @return 处理的卡券数量
     */
    int processExpiredCoupons();
}
