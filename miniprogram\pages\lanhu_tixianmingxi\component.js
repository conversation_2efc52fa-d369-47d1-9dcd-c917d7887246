const httpService = require('../../utils/httpService');
const userService = require('../../utils/userService');

Component({
  properties: {},
  data: {
    withdrawalRecords: [], // 提现记录列表
    allRecords: [], // 所有记录（用于筛选）
    selectedDate: '', // 选中的日期
    selectedDateText: '', // 显示的日期文本
    currentDate: '', // 当前日期
    distributorId: null, // 推荐员ID
    loading: false // 加载状态
  },
  lifetimes: {
    created: function () {
      // 初始化当前日期
      const now = new Date();
      const currentDate = this.formatDate(now);
      const currentYearMonth = now.getFullYear() + '年' + (now.getMonth() + 1) + '月';

      this.setData({
        currentDate: currentDate,
        selectedDate: currentDate,
        selectedDateText: currentYearMonth
      });
    },
    attached: function () {
      console.info("提现明细页面加载");
      this.initPage();
    },
    detached: function () {
      console.info("提现明细页面卸载");
    },
  },
  methods: {
    // 初始化页面
    async initPage() {
      try {
        // 获取用户信息
        const userInfo = await userService.getUserInfo();
        if (!userInfo || !userInfo.id) {
          wx.showToast({
            title: '请先登录',
            icon: 'none'
          });
          return;
        }

        // 设置推荐员ID（这里假设用户ID就是推荐员ID，实际可能需要调整）
        this.setData({
          distributorId: userInfo.id
        });

        // 加载提现记录
        await this.loadWithdrawalRecords();

      } catch (error) {
        console.error('初始化页面失败:', error);
        wx.showToast({
          title: '页面初始化失败',
          icon: 'none'
        });
      }
    },

    // 加载提现记录
    async loadWithdrawalRecords() {
      if (!this.data.distributorId) {
        console.warn('推荐员ID为空，无法加载提现记录');
        return;
      }

      try {
        this.setData({ loading: true });

        console.log('开始加载提现记录，推荐员ID:', this.data.distributorId);

        const response = await httpService.get(`/api/distribution/withdrawal/distributor/${this.data.distributorId}`, {}, {
          showLoading: true,
          loadingText: '加载提现记录中...'
        });

        let records = [];
        if (response && response.data && Array.isArray(response.data)) {
          records = response.data;
        } else if (response && Array.isArray(response)) {
          records = response;
        }

        console.log('获取到提现记录:', records.length, '条');

        // 处理数据
        const processedRecords = this.processWithdrawalRecords(records);

        this.setData({
          allRecords: processedRecords,
          withdrawalRecords: processedRecords
        });

        // 根据当前选择的日期进行筛选
        this.filterRecordsByDate();

      } catch (error) {
        console.error('加载提现记录失败:', error);
        wx.showToast({
          title: '加载失败，请稍后重试',
          icon: 'none'
        });
      } finally {
        this.setData({ loading: false });
      }
    },

    // 处理提现记录数据
    processWithdrawalRecords(records) {
      return records.map(record => {
        // 格式化时间
        const formattedTime = this.formatDateTime(record.appliedAt || record.createdAt);

        // 确定类型文本
        let typeText = '提现申请';
        if (record.status === 'completed') {
          typeText = '提现成功';
        } else if (record.status === 'rejected') {
          typeText = '提现失败';
        }

        // 格式化金额文本
        let amountText = '';
        const amount = parseFloat(record.amount) || 0;
        if (record.status === 'completed') {
          amountText = `-${amount.toFixed(2)}`;
        } else if (record.status === 'rejected') {
          amountText = `+${amount.toFixed(2)}`;
        } else {
          amountText = `-${amount.toFixed(2)}`;
        }

        return {
          ...record,
          typeText: typeText,
          formattedTime: formattedTime,
          amountText: amountText,
          dateKey: this.getDateKey(record.appliedAt || record.createdAt)
        };
      }).sort((a, b) => {
        // 按时间倒序排列
        const timeA = new Date(a.appliedAt || a.createdAt).getTime();
        const timeB = new Date(b.appliedAt || b.createdAt).getTime();
        return timeB - timeA;
      });
    },

    // 日期选择变化
    onDateChange(e) {
      const selectedDate = e.detail.value;
      const date = new Date(selectedDate);
      const yearMonth = date.getFullYear() + '年' + (date.getMonth() + 1) + '月';

      this.setData({
        selectedDate: selectedDate,
        selectedDateText: yearMonth
      });

      // 根据选择的日期筛选记录
      this.filterRecordsByDate();
    },

    // 根据日期筛选记录
    filterRecordsByDate() {
      const selectedDate = this.data.selectedDate;
      if (!selectedDate) {
        this.setData({ withdrawalRecords: this.data.allRecords });
        return;
      }

      const selectedYear = new Date(selectedDate).getFullYear();
      const selectedMonth = new Date(selectedDate).getMonth() + 1;
      const filterKey = `${selectedYear}-${selectedMonth.toString().padStart(2, '0')}`;

      const filteredRecords = this.data.allRecords.filter(record => {
        return record.dateKey.startsWith(filterKey);
      });

      console.log('筛选结果:', filterKey, '共', filteredRecords.length, '条记录');

      this.setData({
        withdrawalRecords: filteredRecords
      });
    },

    // 格式化日期时间
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return '';

      try {
        const date = new Date(dateTimeStr);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}`;
      } catch (error) {
        console.error('日期格式化失败:', error);
        return dateTimeStr;
      }
    },

    // 格式化日期（YYYY-MM-DD）
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 获取日期键（用于筛选）
    getDateKey(dateTimeStr) {
      if (!dateTimeStr) return '';

      try {
        const date = new Date(dateTimeStr);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      } catch (error) {
        console.error('获取日期键失败:', error);
        return '';
      }
    },

    // 刷新数据
    async refreshData() {
      await this.loadWithdrawalRecords();
    }
  },
});
