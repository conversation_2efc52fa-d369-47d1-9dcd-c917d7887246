const apiService = require('../../utils/apiService');
const userService = require('../../utils/userService');

Page({
  data: {
    code: '', // 卡券编码
    couponInfo: null, // 卡券信息
    loading: false,
    claiming: false,
    claimed: false,
    error: null,
    currentUserId: null
  },

  // 页面生命周期
  onLoad: function (options) {
    console.info("卡券领取页面加载", options);
    
    // 获取卡券编码
    if (options.code) {
      this.setData({
        code: options.code
      });
      this.initPage();
    } else {
      this.setData({
        error: '缺少卡券编码参数'
      });
    }
  },

  onShow: function () {
    console.info("卡券领取页面显示");
  },

  onUnload: function () {
    console.info("卡券领取页面卸载");
  },

  // 初始化页面
  async initPage() {
    try {
      // 获取当前用户信息
      let userInfo;
      try {
        const storedUserInfo = wx.getStorageSync('userInfo');
        if (storedUserInfo && storedUserInfo.id) {
          userInfo = storedUserInfo;
        } else {
          userInfo = { id: 1, name: '用户' };
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
        userInfo = { id: 1, name: '用户' };
      }

      this.setData({
        currentUserId: userInfo.id
      });

      // 加载卡券信息
      await this.loadCouponInfo();
    } catch (error) {
      console.error('初始化页面失败:', error);
      this.setData({
        error: '页面加载失败'
      });
    }
  },

  // 加载卡券信息
  async loadCouponInfo() {
    if (this.data.loading) return;

    try {
      this.setData({
        loading: true,
        error: null
      });

      console.log('获取分享卡券信息，code:', this.data.code);
      const couponInfo = await apiService.getShareCouponInfo(this.data.code);

      if (couponInfo) {
        // 获取产品和景区详情
        const enrichedCoupon = await this.enrichCouponWithDetails(couponInfo);
        
        this.setData({
          couponInfo: enrichedCoupon
        });
      } else {
        this.setData({
          error: '卡券不存在'
        });
      }
    } catch (error) {
      console.error('加载卡券信息失败:', error);
      this.setData({
        error: error.message || '加载卡券信息失败'
      });
    } finally {
      this.setData({
        loading: false
      });
    }
  },

  // 为卡券获取产品和景区详情
  async enrichCouponWithDetails(coupon) {
    try {
      const enrichedCoupon = { ...coupon };

      // 获取产品详情
      if (coupon.productId) {
        try {
          const productDetail = await apiService.getProductDetail(coupon.productId);
          enrichedCoupon.productDetail = productDetail;
        } catch (error) {
          console.error('获取产品详情失败:', error);
        }
      }

      // 获取景区详情
      if (enrichedCoupon.productDetail && enrichedCoupon.productDetail.scenicId) {
        try {
          const scenicDetail = await apiService.getScenicDetail(enrichedCoupon.productDetail.scenicId);
          enrichedCoupon.scenicDetail = scenicDetail;
        } catch (error) {
          console.error('获取景区详情失败:', error);
        }
      }

      // 添加显示字段
      enrichedCoupon.productName = enrichedCoupon.productDetail ? enrichedCoupon.productDetail.name : '未知产品';
      enrichedCoupon.productPrice = enrichedCoupon.productDetail ? enrichedCoupon.productDetail.price : 0;
      enrichedCoupon.scenicName = enrichedCoupon.scenicDetail ? enrichedCoupon.scenicDetail.title : '未知景区';
      enrichedCoupon.scenicImage = enrichedCoupon.scenicDetail ? enrichedCoupon.scenicDetail.image : '';
      enrichedCoupon.scenicLocation = enrichedCoupon.scenicDetail ? 
        `${enrichedCoupon.scenicDetail.provinceName || ''}${enrichedCoupon.scenicDetail.cityName || ''}` : '';
      enrichedCoupon.scenicAddress = enrichedCoupon.scenicDetail ? enrichedCoupon.scenicDetail.address : '';

      // 格式化时间
      enrichedCoupon.formattedCreatedAt = this.formatDate(coupon.createdAt);
      enrichedCoupon.formattedValidFrom = this.formatDate(coupon.validFrom);
      enrichedCoupon.formattedValidTo = this.formatDate(coupon.validTo);
      enrichedCoupon.statusText = this.getStatusText(coupon.status);

      return enrichedCoupon;
    } catch (error) {
      console.error('丰富卡券详情失败:', error);
      return coupon;
    }
  },

  // 领取卡券
  async claimCoupon() {
    if (this.data.claiming || this.data.claimed) return;

    const couponInfo = this.data.couponInfo;
    if (!couponInfo) {
      wx.showToast({
        title: '卡券信息不存在',
        icon: 'none'
      });
      return;
    }

    // 检查是否是自己的卡券
    if (couponInfo.userId === this.data.currentUserId) {
      wx.showToast({
        title: '不能领取自己的卡券',
        icon: 'none'
      });
      return;
    }

    try {
      this.setData({
        claiming: true
      });

      wx.showLoading({
        title: '领取中...'
      });

      console.log('领取卡券，code:', this.data.code, 'userId:', this.data.currentUserId);
      const result = await apiService.claimSharedCoupon(this.data.code, this.data.currentUserId);

      wx.hideLoading();

      if (result) {
        this.setData({
          claimed: true
        });

        wx.showToast({
          title: '领取成功',
          icon: 'success'
        });

        // 延迟跳转到我的卡券页面
        setTimeout(() => {
          wx.navigateTo({
            url: '/pages/lanhu_youhuiquanyemiankeshiyong/component'
          });
        }, 1500);
      }
    } catch (error) {
      wx.hideLoading();
      console.error('领取卡券失败:', error);
      wx.showToast({
        title: error.message || '领取失败',
        icon: 'none'
      });
    } finally {
      this.setData({
        claiming: false
      });
    }
  },

  // 查看我的卡券
  goToMyCoupons() {
    wx.navigateTo({
      url: '/pages/lanhu_youhuiquanyemiankeshiyong/component'
    });
  },

  // 返回首页
  goToHome() {
    wx.switchTab({
      url: '/pages/lanhu_shouye/component'
    });
  },

  // 格式化日期
  formatDate(dateString) {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
    } catch (error) {
      return dateString;
    }
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'unactivated': '未激活',
      'active': '已激活',
      'used': '已使用',
      'expired': '已过期'
    };
    return statusMap[status] || status;
  }
});
