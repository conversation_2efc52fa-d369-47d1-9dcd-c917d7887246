<!-- 卡券领取页面 -->
<view class="page">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-content">
      <text class="loading-text">加载中...</text>
    </view>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{error}}" class="error-container">
    <view class="error-content">
      <view class="error-icon">❌</view>
      <text class="error-text">{{error}}</text>
      <button class="retry-btn" bindtap="loadCouponInfo">重试</button>
      <button class="home-btn" bindtap="goToHome">返回首页</button>
    </view>
  </view>

  <!-- 已领取状态 -->
  <view wx:elif="{{claimed}}" class="success-container">
    <view class="success-content">
      <view class="success-icon">✅</view>
      <text class="success-title">领取成功！</text>
      <text class="success-desc">卡券已添加到您的账户中</text>
      <button class="my-coupons-btn" bindtap="goToMyCoupons">查看我的卡券</button>
      <button class="home-btn" bindtap="goToHome">返回首页</button>
    </view>
  </view>

  <!-- 卡券信息展示 -->
  <view wx:else class="coupon-container">
    <view wx:if="{{couponInfo}}" class="coupon-card">
      <!-- 卡券头部 -->
      <view class="coupon-header">
        <text class="share-title">朋友分享给您一张卡券</text>
      </view>

      <!-- 卡券内容 -->
      <view class="coupon-content">
        <!-- 景区信息 -->
        <view class="scenic-info">
          <view wx:if="{{couponInfo.scenicImage}}" class="scenic-image">
            <image src="{{couponInfo.scenicImage}}" mode="aspectFill" class="scenic-img"></image>
          </view>
          <view class="scenic-details">
            <text class="scenic-name">{{couponInfo.scenicName}}</text>
            <text class="scenic-location">{{couponInfo.scenicLocation}}</text>
            <text class="scenic-address">{{couponInfo.scenicAddress}}</text>
          </view>
        </view>

        <!-- 卡券详情 -->
        <view class="coupon-details">
          <view class="detail-row">
            <text class="detail-label">产品名称：</text>
            <text class="detail-value">{{couponInfo.productName}}</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">产品价格：</text>
            <text class="detail-value price">¥{{couponInfo.productPrice}}</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">卡券状态：</text>
            <text class="detail-value status">{{couponInfo.statusText}}</text>
          </view>
          <view wx:if="{{couponInfo.formattedValidFrom}}" class="detail-row">
            <text class="detail-label">生效时间：</text>
            <text class="detail-value">{{couponInfo.formattedValidFrom}}</text>
          </view>
          <view wx:if="{{couponInfo.formattedValidTo}}" class="detail-row">
            <text class="detail-label">过期时间：</text>
            <text class="detail-value">{{couponInfo.formattedValidTo}}</text>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button 
          class="claim-btn {{claiming ? 'claiming' : ''}}" 
          bindtap="claimCoupon"
          disabled="{{claiming}}"
        >
          {{claiming ? '领取中...' : '立即领取'}}
        </button>
        <button class="cancel-btn" bindtap="goToHome">取消</button>
      </view>
    </view>

    <!-- 温馨提示 -->
    <view class="tips-container">
      <view class="tips-header">
        <text class="tips-title">温馨提示</text>
      </view>
      <view class="tips-content">
        <text class="tips-item">• 领取后卡券将转移到您的账户中</text>
        <text class="tips-item">• 请确认卡券信息无误后再领取</text>
        <text class="tips-item">• 每张卡券只能领取一次</text>
        <text class="tips-item">• 领取后可在"我的卡券"中查看</text>
      </view>
    </view>
  </view>
</view>
