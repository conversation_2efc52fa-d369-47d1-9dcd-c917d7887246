package com.tourism.miniprogram.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.entity.Product;
import com.tourism.miniprogram.service.ProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 产品控制器
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@RestController
@RequestMapping("/products")
@Api(tags = "产品管理")
public class ProductController {

    @Autowired
    private ProductService productService;

    /**
     * 获取产品列表
     */
    @GetMapping
    @ApiOperation(value = "获取产品列表", notes = "获取所有启用的产品列表")
    public Result<List<Product>> getProducts() {
        try {
            List<Product> products = productService.getEnabledProducts();
            return Result.success(products);
        } catch (Exception e) {
            log.error("获取产品列表失败", e);
            return Result.error("获取产品列表失败");
        }
    }

    /**
     * 分页获取产品列表
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页获取产品列表", notes = "分页获取产品列表，支持按名称、景区ID、产品类型、订单ID、套餐ID筛选")
    public Result<IPage<Product>> getProductPage(
            @ApiParam(value = "当前页", defaultValue = "1") @RequestParam(defaultValue = "1") Long current,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") Long size,
            @ApiParam(value = "产品名称") @RequestParam(required = false) String name,
            @ApiParam(value = "景区ID") @RequestParam(required = false) Integer scenicId,
            @ApiParam(value = "产品类型") @RequestParam(required = false) String productType,
            @ApiParam(value = "订单ID") @RequestParam(required = false) Integer orderId,
            @ApiParam(value = "套餐产品ID") @RequestParam(required = false) Long bundleId,
            @ApiParam(value = "状态") @RequestParam(required = false) Integer status) {
        try {
            Page<Product> page = new Page<>(current, size);
            QueryWrapper<Product> queryWrapper = new QueryWrapper<>();

            if (StringUtils.hasText(name)) {
                queryWrapper.like("name", name);
            }
            if (scenicId != null) {
                queryWrapper.eq("scenic_id", scenicId);
            }
            if (StringUtils.hasText(productType)) {
                queryWrapper.eq("product_type", productType);
            }
            if (orderId != null) {
                queryWrapper.eq("order_id", orderId);
            }
            if (bundleId != null) {
                queryWrapper.eq("bundle_id", bundleId);
            }
            if (status != null) {
                queryWrapper.eq("status", status);
            }
            queryWrapper.orderByDesc("id");

            IPage<Product> productPage = productService.page(page, queryWrapper);
            return Result.success(productPage);
        } catch (Exception e) {
            log.error("分页获取产品列表失败", e);
            return Result.error("获取产品列表失败");
        }
    }

    /**
     * 获取产品详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取产品详情", notes = "根据ID获取产品详细信息")
    public Result<Product> getProductById(@ApiParam(value = "产品ID", required = true) @PathVariable Integer id) {
        try {
            Product product = productService.getById(id);
            if (product == null) {
                return Result.error(404, "产品不存在");
            }
            return Result.success(product);
        } catch (Exception e) {
            log.error("获取产品详情失败，id: {}", id, e);
            return Result.error("获取产品详情失败");
        }
    }

    /**
     * 根据景区ID获取产品列表
     */
    @GetMapping("/scenic/{scenicId}")
    @ApiOperation(value = "根据景区ID获取产品列表", notes = "根据景区ID获取该景区的所有产品")
    public Result<List<Product>> getProductsByScenicId(@ApiParam(value = "景区ID", required = true) @PathVariable Integer scenicId) {
        try {
            List<Product> products = productService.getProductsByScenicId(scenicId);
            return Result.success(products);
        } catch (Exception e) {
            log.error("根据景区ID获取产品列表失败，scenicId: {}", scenicId, e);
            return Result.error("获取产品列表失败");
        }
    }

    /**
     * 根据产品类型获取产品列表
     */
    @GetMapping("/type/{productType}")
    @ApiOperation(value = "根据产品类型获取产品列表", notes = "根据产品类型获取产品列表")
    public Result<List<Product>> getProductsByType(@ApiParam(value = "产品类型", required = true) @PathVariable String productType) {
        try {
            List<Product> products = productService.getProductsByType(productType);
            return Result.success(products);
        } catch (Exception e) {
            log.error("根据产品类型获取产品列表失败，productType: {}", productType, e);
            return Result.error("获取产品列表失败");
        }
    }

    /**
     * 根据订单ID获取产品列表
     */
    @GetMapping("/order/{orderId}")
    @ApiOperation(value = "根据订单ID获取产品列表", notes = "根据订单ID获取该订单的所有产品")
    public Result<List<Product>> getProductsByOrderId(@ApiParam(value = "订单ID", required = true) @PathVariable Integer orderId) {
        try {
            List<Product> products = productService.getProductsByOrderId(orderId);
            return Result.success(products);
        } catch (Exception e) {
            log.error("根据订单ID获取产品列表失败，orderId: {}", orderId, e);
            return Result.error("获取产品列表失败");
        }
    }

    /**
     * 根据套餐ID获取产品列表
     */
    @GetMapping("/bundle/{bundleId}")
    @ApiOperation(value = "根据套餐ID获取产品列表", notes = "根据套餐ID获取该套餐的所有产品")
    public Result<List<Product>> getProductsByBundleId(@ApiParam(value = "套餐ID", required = true) @PathVariable Long bundleId) {
        try {
            QueryWrapper<Product> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("bundle_id", bundleId);
            queryWrapper.eq("status", 1);
            queryWrapper.orderByDesc("id");
            List<Product> products = productService.list(queryWrapper);
            return Result.success(products);
        } catch (Exception e) {
            log.error("根据套餐ID获取产品列表失败，bundleId: {}", bundleId, e);
            return Result.error("获取产品列表失败");
        }
    }

    /**
     * 创建产品
     */
    @PostMapping
    @ApiOperation(value = "创建产品", notes = "创建新的产品")
    public Result<Product> createProduct(@RequestBody @Valid Product product) {
        try {
            boolean success = productService.save(product);
            if (success) {
                // 重新查询获取完整的产品信息（包含自动生成的ID和时间戳）
                Product createdProduct = productService.getById(product.getId());
                return Result.success(createdProduct);
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建产品失败", e);
            return Result.error("创建产品失败");
        }
    }

    /**
     * 更新产品
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "更新产品", notes = "更新产品信息")
    public Result<String> updateProduct(
            @ApiParam(value = "产品ID", required = true) @PathVariable Integer id,
            @RequestBody @Valid Product product) {
        try {
            Product existProduct = productService.getById(id);
            if (existProduct == null) {
                return Result.error(404, "产品不存在");
            }

            product.setId(id);
            boolean success = productService.updateById(product);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新产品失败，id: {}", id, e);
            return Result.error("更新产品失败");
        }
    }

    /**
     * 删除产品
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除产品", notes = "删除产品")
    public Result<String> deleteProduct(@ApiParam(value = "产品ID", required = true) @PathVariable Integer id) {
        try {
            Product product = productService.getById(id);
            if (product == null) {
                return Result.error(404, "产品不存在");
            }

            boolean success = productService.removeById(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除产品失败，id: {}", id, e);
            return Result.error("删除产品失败");
        }
    }
}
