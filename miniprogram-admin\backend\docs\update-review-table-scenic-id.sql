-- 更新评价表结构：将product_id字段改为scenic_id
-- 执行时间：2025-06-21
-- 说明：根据业务需求，评价表中的产品ID字段需要改为景区ID

-- 1. 首先备份现有数据（可选，建议在生产环境执行）
-- CREATE TABLE review_table_backup AS SELECT * FROM review_table;

-- 2. 添加新的scenic_id字段
ALTER TABLE `review_table` ADD COLUMN `scenic_id` VARCHAR(255) NOT NULL COMMENT '景区ID' AFTER `user_id`;

-- 3. 如果需要迁移现有数据，可以通过产品表关联获取景区ID
-- 注意：这个步骤需要根据实际的产品表结构调整
-- UPDATE review_table r 
-- JOIN products p ON r.product_id = p.id 
-- SET r.scenic_id = p.scenic_id 
-- WHERE r.product_id IS NOT NULL;

-- 4. 删除旧的product_id字段
-- 注意：在确认数据迁移正确后再执行此步骤
-- ALTER TABLE `review_table` DROP COLUMN `product_id`;

-- 5. 添加索引以优化查询性能
CREATE INDEX `idx_review_scenic_id` ON `review_table` (`scenic_id`);
CREATE INDEX `idx_review_user_scenic` ON `review_table` (`user_id`, `scenic_id`);

-- 6. 验证表结构
-- DESCRIBE review_table;

-- 7. 验证数据
-- SELECT COUNT(*) as total_reviews FROM review_table;
-- SELECT scenic_id, COUNT(*) as review_count FROM review_table GROUP BY scenic_id;

-- 注意事项：
-- 1. 在生产环境执行前，请先在测试环境验证
-- 2. 建议在业务低峰期执行
-- 3. 执行前请备份数据库
-- 4. 如果有现有数据需要迁移，请根据实际情况调整迁移脚本
-- 5. 删除product_id字段前，请确认所有相关代码已更新完成
