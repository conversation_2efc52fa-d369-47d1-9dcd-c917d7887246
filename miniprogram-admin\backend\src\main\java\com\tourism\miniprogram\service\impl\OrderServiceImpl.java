package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.entity.Order;
import com.tourism.miniprogram.entity.Coupon;
import com.tourism.miniprogram.entity.Product;
import com.tourism.miniprogram.entity.ProductBundle;
import com.tourism.miniprogram.entity.BundleProduct;
import com.tourism.miniprogram.mapper.OrderMapper;
import com.tourism.miniprogram.service.OrderService;
import com.tourism.miniprogram.service.CouponService;
import com.tourism.miniprogram.service.ProductService;
import com.tourism.miniprogram.service.ProductBundleService;
import com.tourism.miniprogram.service.BundleProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 订单服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service
public class OrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements OrderService {

    @Autowired
    private CouponService couponService;

    @Autowired
    private ProductService productService;

    @Autowired
    private ProductBundleService productBundleService;

    @Autowired
    private BundleProductService bundleProductService;

    @Override
    public List<Order> getOrdersByUserId(Integer userId) {
        try {
            return baseMapper.selectOrdersByUserId(userId);
        } catch (Exception e) {
            log.error("根据用户ID获取订单列表失败，userId: {}", userId, e);
            throw new RuntimeException("根据用户ID获取订单列表失败");
        }
    }

    @Override
    public List<Order> getOrdersByStatus(String status) {
        try {
            return baseMapper.selectOrdersByStatus(status);
        } catch (Exception e) {
            log.error("根据状态获取订单列表失败，status: {}", status, e);
            throw new RuntimeException("根据状态获取订单列表失败");
        }
    }

    @Override
    public boolean isFirstOrder(Integer userId) {
        try {
            Integer orderCount = baseMapper.countOrdersByUserId(userId);
            return orderCount == 0;
        } catch (Exception e) {
            log.error("检查用户是否为首次下单失败，userId: {}", userId, e);
            throw new RuntimeException("检查用户首次下单失败");
        }
    }

    @Override
    public Integer getUserOrderCount(Integer userId) {
        try {
            return baseMapper.countOrdersByUserId(userId);
        } catch (Exception e) {
            log.error("获取用户订单数量失败，userId: {}", userId, e);
            throw new RuntimeException("获取用户订单数量失败");
        }
    }

    @Override
    public Integer getUserPaidOrderCount(Integer userId) {
        try {
            return baseMapper.countPaidOrdersByUserId(userId);
        } catch (Exception e) {
            log.error("获取用户已支付订单数量失败，userId: {}", userId, e);
            throw new RuntimeException("获取用户已支付订单数量失败");
        }
    }

    @Override
    @Transactional
    public boolean createOrderWithCoupons(Order order) {
        try {
            // 生成订单号
            order.setOrderNo(generateOrderNo());
            order.setStatus("pending");

            // 保存订单
            boolean orderSaved = save(order);
            if (!orderSaved) {
                log.error("保存订单失败");
                return false;
            }

            // 注释掉自动生成门票的逻辑，改为由前端控制门票创建
            // 这样可以避免创建没有景区信息的空门票
            // boolean couponsGenerated = generateCouponsForOrder(order);
            // if (!couponsGenerated) {
            //     log.error("生成门票失败，订单ID: {}", order.getId());
            //     return false;
            // }

            log.info("订单创建成功，订单ID: {}, 订单号: {}", order.getId(), order.getOrderNo());
            return true;

        } catch (Exception e) {
            log.error("创建订单失败", e);
            throw new RuntimeException("创建订单失败");
        }
    }

    @Override
    @Transactional
    public boolean payOrder(Integer orderId) {
        try {
            Order order = getById(orderId);
            if (order == null) {
                log.error("订单不存在，orderId: {}", orderId);
                return false;
            }

            if (!"pending".equals(order.getStatus())) {
                log.error("订单状态不正确，无法支付，orderId: {}, status: {}", orderId, order.getStatus());
                return false;
            }

            // 更新订单状态为已支付
            order.setStatus("paid");
            boolean updated = updateById(order);

            if (updated) {
                log.info("订单支付成功，订单ID: {}", orderId);
                return true;
            } else {
                log.error("更新订单状态失败，订单ID: {}", orderId);
                return false;
            }

        } catch (Exception e) {
            log.error("支付订单失败，orderId: {}", orderId, e);
            throw new RuntimeException("支付订单失败");
        }
    }

    @Override
    @Transactional
    public boolean refundOrder(Integer orderId) {
        try {
            Order order = getById(orderId);
            if (order == null) {
                log.error("订单不存在，orderId: {}", orderId);
                return false;
            }

            if (!"paid".equals(order.getStatus())) {
                log.error("订单状态不正确，无法退款，orderId: {}, status: {}", orderId, order.getStatus());
                return false;
            }

            // 查询订单对应的卡券
            List<Coupon> orderCoupons = couponService.getCouponsByOrderId(orderId);

            // 检查卡券是否都是未激活状态
            for (Coupon coupon : orderCoupons) {
                if (!"unactivated".equals(coupon.getStatus())) {
                    log.error("订单包含已激活的卡券，无法退款，orderId: {}, couponId: {}, status: {}",
                             orderId, coupon.getId(), coupon.getStatus());
                    return false;
                }
            }

            // 更新订单状态为已取消
            order.setStatus("canceled");
            boolean orderUpdated = updateById(order);

            if (!orderUpdated) {
                log.error("更新订单状态失败，订单ID: {}", orderId);
                return false;
            }

            // 更新所有相关卡券状态为过期
            for (Coupon coupon : orderCoupons) {
                coupon.setStatus("expired");
                boolean couponUpdated = couponService.updateById(coupon);
                if (!couponUpdated) {
                    log.error("更新卡券状态失败，卡券ID: {}", coupon.getId());
                    // 这里可以选择回滚或继续处理其他卡券
                }
            }

            log.info("订单退款成功，订单ID: {}, 处理卡券数量: {}", orderId, orderCoupons.size());
            return true;

        } catch (Exception e) {
            log.error("退款订单失败，orderId: {}", orderId, e);
            throw new RuntimeException("退款订单失败");
        }
    }

    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        return "ORD" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 6).toUpperCase();
    }

    /**
     * 为订单生成门票
     */
    private boolean generateCouponsForOrder(Order order) {
        try {
            // 这里需要根据订单中的产品信息生成相应的门票
            // 由于Order实体中没有直接的产品信息，这里提供一个基本的实现框架
            
            // 假设订单中包含产品ID或组合包ID的信息
            // 实际实现时需要根据具体的业务需求调整
            
            // 示例：为订单生成一张基本门票
            Coupon coupon = new Coupon();
            coupon.setCode(couponService.generateCouponCode());
            coupon.setOrderId(order.getId());
            coupon.setUserId(order.getUserId());
            // 景区ID字段已移除，门票不再关联特定景区
            coupon.setStatus("unactivated");
            coupon.setCreatedAt(LocalDateTime.now());

            boolean couponSaved = couponService.save(coupon);
            if (couponSaved) {
                log.info("为订单生成门票成功，订单ID: {}, 门票ID: {}", order.getId(), coupon.getId());
                return true;
            } else {
                log.error("保存门票失败，订单ID: {}", order.getId());
                return false;
            }

        } catch (Exception e) {
            log.error("为订单生成门票失败，订单ID: {}", order.getId(), e);
            return false;
        }
    }
}
