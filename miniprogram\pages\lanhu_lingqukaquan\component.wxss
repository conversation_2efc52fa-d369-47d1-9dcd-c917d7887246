/* 页面容器 */
.page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 20rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading-content {
  text-align: center;
}

.loading-text {
  font-size: 32rpx;
  color: #666;
}

/* 错误状态 */
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.error-content {
  text-align: center;
  padding: 40rpx;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 30rpx;
}

.error-text {
  display: block;
  font-size: 32rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.retry-btn, .home-btn {
  width: 200rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  border-radius: 40rpx;
  margin: 10rpx;
}

.retry-btn {
  background-color: #007aff;
  color: white;
}

.home-btn {
  background-color: #f0f0f0;
  color: #333;
}

/* 成功状态 */
.success-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.success-content {
  text-align: center;
  padding: 40rpx;
}

.success-icon {
  font-size: 80rpx;
  margin-bottom: 30rpx;
}

.success-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #28a745;
  margin-bottom: 20rpx;
}

.success-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.my-coupons-btn {
  width: 240rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  border-radius: 40rpx;
  background-color: #28a745;
  color: white;
  margin: 10rpx;
}

/* 卡券容器 */
.coupon-container {
  padding: 20rpx;
}

.coupon-card {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 卡券头部 */
.coupon-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30rpx;
  text-align: center;
}

.share-title {
  font-size: 32rpx;
  color: white;
  font-weight: bold;
}

/* 卡券内容 */
.coupon-content {
  padding: 30rpx;
}

/* 景区信息 */
.scenic-info {
  display: flex;
  margin-bottom: 30rpx;
}

.scenic-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 10rpx;
  overflow: hidden;
  margin-right: 20rpx;
}

.scenic-img {
  width: 100%;
  height: 100%;
}

.scenic-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.scenic-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.scenic-location {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.scenic-address {
  font-size: 24rpx;
  color: #999;
}

/* 卡券详情 */
.coupon-details {
  border-top: 1rpx solid #eee;
  padding-top: 20rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
}

.detail-value.price {
  color: #ff6b35;
  font-weight: bold;
}

.detail-value.status {
  color: #007aff;
}

/* 操作按钮 */
.action-buttons {
  padding: 30rpx;
  display: flex;
  gap: 20rpx;
}

.claim-btn {
  flex: 2;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  border-radius: 44rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: bold;
}

.claim-btn.claiming {
  background: #ccc;
}

.cancel-btn {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 28rpx;
  border-radius: 44rpx;
  background-color: #f0f0f0;
  color: #666;
}

/* 温馨提示 */
.tips-container {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

.tips-header {
  margin-bottom: 20rpx;
}

.tips-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.tips-content {
  display: flex;
  flex-direction: column;
}

.tips-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 10rpx;
}
