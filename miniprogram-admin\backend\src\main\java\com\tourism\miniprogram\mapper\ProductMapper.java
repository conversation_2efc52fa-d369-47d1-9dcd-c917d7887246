package com.tourism.miniprogram.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tourism.miniprogram.entity.Product;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 产品Mapper接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Mapper
public interface ProductMapper extends BaseMapper<Product> {

    /**
     * 获取启用的产品列表
     *
     * @return 产品列表
     */
    @Select("SELECT * FROM products WHERE status = 1 ORDER BY id DESC")
    List<Product> selectEnabledProducts();

    /**
     * 根据景区ID获取产品列表
     *
     * @param scenicId 景区ID
     * @return 产品列表
     */
    @Select("SELECT * FROM products WHERE scenic_id = #{scenicId} AND status = 1 ORDER BY id DESC")
    List<Product> selectProductsByScenicId(Integer scenicId);

    /**
     * 根据产品类型获取产品列表
     *
     * @param productType 产品类型
     * @return 产品列表
     */
    @Select("SELECT * FROM products WHERE product_type = #{productType} AND status = 1 ORDER BY id DESC")
    List<Product> selectProductsByType(String productType);

    /**
     * 根据订单ID获取产品列表
     *
     * @param orderId 订单ID
     * @return 产品列表
     */
    @Select("SELECT * FROM products WHERE order_id = #{orderId} ORDER BY id DESC")
    List<Product> selectProductsByOrderId(Integer orderId);

    /**
     * 根据套餐ID获取产品列表
     *
     * @param bundleId 套餐ID
     * @return 产品列表
     */
    @Select("SELECT * FROM products WHERE bundle_id = #{bundleId} AND status = 1 ORDER BY id DESC")
    List<Product> selectProductsByBundleId(Long bundleId);
}
