<view class="page" bindtap="onPageTap">
  <view class="section_1">
    <view class="section_2">
      <image src="" class="thumbnail_3"></image>
      <text lines="1" class="text_1">我的</text>
    </view>

    <!-- 用户信息区域 -->
    <view class="section_3">
      <view class="image-text_1">
        <!-- 用户头像 -->
        <image
          src="{{isLoggedIn && userInfo.avatarUrl ? userInfo.avatarUrl : '../../images/lanhu_wode/FigmaDDSSlicePNG10c73748a165340d71f1599a6b709095.png'}}"
          class="image_3">
        </image>

        <view class="text-group_1">
          <!-- 用户昵称 -->
          <text lines="1" class="text_2">
            {{isLoggedIn && userInfo.nickname ? userInfo.nickname : '微信用户'}}
          </text>

          <!-- 手机号显示/授权按钮 -->
          <view class="phone-info">
            <text wx:if="{{isLoggedIn && userInfo.phone}}" class="phone-text">
              {{userInfo.phone}}
            </text>
            <button wx:elif="{{isLoggedIn}}"
                    class="auth-phone-btn"
                    bindtap="handleUserAuth">
              授权手机号
            </button>
          </view>

        </view>
      </view>
    </view>
    <view class="section_4">
      <view class="image-text_3" bindtap="onMyCardsClick">
        <image src="../../images/lanhu_wode/FigmaDDSSlicePNGbdb83408567a84b05aac9f6289ab4ee8.png" class="label_1"></image>
        <text lines="1" class="text-group_3">我的卡券</text>
      </view>
      <image src="../../images/lanhu_wode/FigmaDDSSlicePNGc54f55a34e4646c531d9fe1b439edd6c.png" class="image_4"></image>
      <view class="image-text_4" bindtap="onMyActivationCodeClick">
        <image src="../../images/lanhu_wode/FigmaDDSSlicePNGcd8571f6db313666ace86a8145243fba.png" class="label_2"></image>
        <text lines="1" class="text-group_4">我的激活码</text>
      </view>
      <image src="../../images/lanhu_wode/FigmaDDSSlicePNGc54f55a34e4646c531d9fe1b439edd6c.png" class="image_5"></image>
      <view class="image-text_5" bindtap="onActivateClick">
        <image src="../../images/lanhu_wode/FigmaDDSSlicePNG784c8975a0264b4110b92c62b3d49d02.png" class="label_3"></image>
        <text lines="1" class="text-group_5">去激活</text>
      </view>
    </view>
  </view>
  <view class="section_5">
    <view class="text-wrapper_2">
      <text lines="1" class="text_5">功能服务</text>
    </view>
    <view class="box_1" bindtap="onFeedbackClick">
      <view class="image-text_6">
        <image src="../../images/lanhu_wode/FigmaDDSSlicePNG8ac9b3b77582cd2c8936d4cadd8f5d94.png" class="thumbnail_5"></image>
        <text lines="1" class="text-group_6">意见反馈</text>
      </view>
      <image src="../../images/lanhu_wode/FigmaDDSSlicePNG408afc6279828a566b872d40fbf966d5.png" class="thumbnail_6"></image>
    </view>
    <view class="box_2" bindtap="onBusinessCooperationClick">
      <view class="image-text_7">
        <image src="/images/商务合作.png" class="group_1"></image>
        <text lines="1" class="text-group_7">商务合作</text>
      </view>
      <image src="../../images/lanhu_wode/FigmaDDSSlicePNG408afc6279828a566b872d40fbf966d5.png" class="thumbnail_7"></image>
    </view>
    <view class="box_3" bindtap="onMyDistributionClick">
      <view class="image-text_8">
        <image class="box_4" src="/images/我的分销.png"></image>
        <text lines="1" class="text-group_8">我的推荐</text>
      </view>
      <image src="../../images/lanhu_wode/FigmaDDSSlicePNG408afc6279828a566b872d40fbf966d5.png" class="thumbnail_8"></image>
    </view>
    <view class="box_5" bindtap="onSettingsClick">
      <view class="image-text_9">
        <image src="../../images/lanhu_wode/FigmaDDSSlicePNG8157a5f618a672abcd30f02cf0b4796e.png" class="thumbnail_9"></image>
        <text lines="1" class="text-group_9">设置</text>
      </view>
      <image src="../../images/lanhu_wode/FigmaDDSSlicePNG408afc6279828a566b872d40fbf966d5.png" class="thumbnail_10"></image>
    </view>
  </view>
  <view class="section_8">
    <view class="box_6" bindtap="onMyOrdersClick">
      <text lines="1" class="text_6">我的订单</text>
      <text lines="1" class="text_7">查看全部</text>
      <image src="../../images/lanhu_wode/FigmaDDSSlicePNG19795411de0895d6ea90ac134221833f.png" class="thumbnail_12"></image>
    </view>
    <view class="box_7">
      <view class="image-text_12" bindtap="onOrderStatusClick" data-status="pending">
        <image src="../../images/lanhu_wode/FigmaDDSSlicePNG305cf1cbe7029645c9595d6ac30fb828.png" class="image_7"></image>
        <text lines="1" class="text-group_12">待付款</text>
      </view>
      <view class="image-text_13" bindtap="onOrderStatusClick" data-status="review">
        <image src="../../images/lanhu_wode/FigmaDDSSlicePNG3fde401c6093fa223aee3e33fefc8b55.png" class="thumbnail_13"></image>
        <text lines="1" class="text-group_13">待评价</text>
      </view>
      <view class="image-text_14" bindtap="onOrderStatusClick" data-status="paid">
        <image src="../../images/lanhu_wode/FigmaDDSSlicePNGf08b8ec888592af3a8376afc978a223f.png" class="image_8"></image>
        <text lines="1" class="text-group_14">已付款</text>
      </view>
      <view class="image-text_15" bindtap="onOrderStatusClick" data-status="canceled">
        <image src="../../images/lanhu_wode/FigmaDDSSlicePNGa25790c4e65ecdd95bdc05600c74b676.png" class="thumbnail_14"></image>
        <text lines="1" class="text-group_15">已取消</text>
      </view>
    </view>
  </view>

  <!-- 手机号授权弹窗 -->
  <view class="phone-auth-modal" wx:if="{{showPhoneAuthModal}}">
    <view class="modal-mask" bindtap="closePhoneAuthModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">手机号授权</text>
        <view class="modal-close" bindtap="closePhoneAuthModal">×</view>
      </view>
      <view class="modal-body">
        <text class="modal-desc">为了更好地为您提供服务，请授权您的手机号</text>
        <button class="phone-auth-button"
                open-type="getPhoneNumber"
                bindgetphonenumber="onGetPhoneNumber">
          授权手机号
        </button>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{isLoading}}">
    <view class="loading-content">
      <text class="loading-text">处理中...</text>
    </view>
  </view>
</view>