// 产品服务模块
const httpService = require('./httpService');

class ProductService {
  constructor() {
    this.cacheKeyPrefix = 'products_cache_';
    this.cacheTime = 5 * 60 * 1000; // 5分钟缓存
  }

  // 创建产品
  async createProduct(productData) {
    try {
      if (!productData.name) {
        throw new Error('产品名称不能为空');
      }
      if (!productData.price) {
        throw new Error('产品价格不能为空');
      }
      if (!productData.scenicId) {
        throw new Error('景区ID不能为空');
      }

      console.log('创建产品:', productData);

      const product = await httpService.post('/api/products', {
        name: productData.name,
        description: productData.description || '',
        price: productData.price,
        scenicId: productData.scenicId,
        orderId: productData.orderId, // 添加订单ID字段
        bundleId: productData.bundleId, // 添加套餐ID字段
        productType: productData.productType || 'single',
        status: productData.status || 1,
        requireActivation: productData.requireActivation !== false,
        validityHours: productData.validityHours || 24,
        image: productData.image || '',
        category: productData.category || 'guide',
        tags: productData.tags || '',
        sort: productData.sort || 0
      }, {
        loadingText: '创建产品中...'
      });

      console.log('产品创建成功:', product);
      return product;
    } catch (error) {
      console.error('创建产品失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 获取产品详情
  async getProductDetail(productId) {
    try {
      if (!productId) {
        throw new Error('产品ID不能为空');
      }

      // 构建缓存key
      const cacheKey = this.buildCacheKey('detail', { productId });

      // 检查缓存
      const cachedData = this.getCache(cacheKey);
      if (cachedData) {
        console.log('使用缓存的产品详情数据');
        return cachedData;
      }

      console.log(`从服务器获取产品详情 (产品ID: ${productId})`);
      const product = await httpService.get(`/api/products/${productId}`, {}, {
        loadingText: '加载产品详情中...'
      });

      // 缓存数据
      this.setCache(cacheKey, product);

      console.log('产品详情获取成功:', product);
      return product;
    } catch (error) {
      console.error('获取产品详情失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 更新产品信息
  async updateProduct(productId, updateData) {
    try {
      if (!productId) {
        throw new Error('产品ID不能为空');
      }

      console.log('更新产品信息:', productId, updateData);

      const result = await httpService.put(`/api/products/${productId}`, updateData, {
        loadingText: '更新产品中...'
      });

      // 清除相关缓存
      this.clearProductCache(productId);

      console.log('产品更新成功:', result);
      return result;
    } catch (error) {
      console.error('更新产品失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 获取产品列表
  async getProductList(params = {}) {
    try {
      const {
        scenicId,
        category,
        status,
        current = 1,
        size = 20
      } = params;

      const queryParams = {
        current: current.toString(),
        size: size.toString()
      };

      if (scenicId) {
        queryParams.scenicId = scenicId.toString();
      }

      if (category) {
        queryParams.category = category;
      }

      if (status !== undefined) {
        queryParams.status = status.toString();
      }

      const products = await httpService.get('/api/products/page', queryParams, {
        loadingText: '加载产品列表中...'
      });

      return products;
    } catch (error) {
      console.error('获取产品列表失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 删除产品
  async deleteProduct(productId) {
    try {
      if (!productId) {
        throw new Error('产品ID不能为空');
      }

      console.log('删除产品:', productId);

      const result = await httpService.delete(`/api/products/${productId}`, {}, {
        loadingText: '删除产品中...'
      });

      // 清除相关缓存
      this.clearProductCache(productId);

      console.log('产品删除成功:', result);
      return result;
    } catch (error) {
      console.error('删除产品失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 根据订单ID获取产品列表
  async getProductsByOrderId(orderId) {
    try {
      if (!orderId) {
        throw new Error('订单ID不能为空');
      }

      console.log('根据订单ID获取产品列表:', orderId);

      const products = await httpService.get(`/api/products/order/${orderId}`, {}, {
        loadingText: '加载产品列表中...'
      });

      console.log('根据订单ID获取产品列表成功:', products);
      return products;
    } catch (error) {
      console.error('根据订单ID获取产品列表失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 缓存相关方法
  buildCacheKey(type, params) {
    const paramsStr = Object.keys(params)
      .sort()
      .map(key => `${key}=${params[key]}`)
      .join('&');
    return `${this.cacheKeyPrefix}${type}_${paramsStr}`;
  }

  getCache(cacheKey) {
    try {
      const cached = wx.getStorageSync(cacheKey);
      if (cached && cached.timestamp) {
        const now = Date.now();
        if (now - cached.timestamp < this.cacheTime) {
          return cached.data;
        } else {
          // 缓存过期，清除
          wx.removeStorageSync(cacheKey);
        }
      }
    } catch (error) {
      console.error('读取产品缓存失败:', error);
    }
    return null;
  }

  setCache(cacheKey, data) {
    try {
      wx.setStorageSync(cacheKey, {
        data: data,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('设置产品缓存失败:', error);
    }
  }

  // 清除指定产品的缓存
  clearProductCache(productId) {
    try {
      const keys = wx.getStorageInfoSync().keys;
      keys.forEach(key => {
        if (key.startsWith(this.cacheKeyPrefix) && key.includes(`productId=${productId}`)) {
          wx.removeStorageSync(key);
        }
      });
      console.log(`产品${productId}的缓存已清除`);
    } catch (error) {
      console.error('清除产品缓存失败:', error);
    }
  }

  // 清除所有缓存
  clearCache() {
    try {
      const keys = wx.getStorageInfoSync().keys;
      keys.forEach(key => {
        if (key.startsWith(this.cacheKeyPrefix)) {
          wx.removeStorageSync(key);
        }
      });
      console.log('产品缓存已清除');
    } catch (error) {
      console.error('清除产品缓存失败:', error);
    }
  }
}

// 创建单例实例
const productService = new ProductService();

module.exports = productService;
