/* 页面容器 */
.page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 标签栏 */
.tab-bar {
  background-color: #fff;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #e5e5e5;
  position: sticky;
  top: 0;
  z-index: 100;
}

.tab-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
  position: relative;
}

.tab-item.active .tab-text {
  color: #007aff;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 4rpx;
  background-color: #007aff;
  border-radius: 2rpx;
}

.tab-text {
  font-size: 30rpx;
  color: #333;
}

/* 门票列表 */
.coupon-list {
  padding: 30rpx 20rpx;
}

.coupon-item {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  padding: 20rpx 20rpx;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
}

.coupon-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
  background-size: 50rpx 50rpx;
  pointer-events: none;
}

/* 门票头部 */
.coupon-header {
  margin-bottom: 30rpx;
}

.coupon-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.title-text {
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 32rpx;
  color: #000000;
  text-align: left;
  font-style: normal;
  text-transform: none;
  flex: 1;
}

.coupon-price {
  margin-left: 20rpx;
}

.price-text {
  font-size: 28rpx;
  color: #ff6b35;
  font-weight: bold;
}

.status-badge {
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.status-badge.status-unactivated {
  background-color: rgba(255, 193, 7, 0.9);
  color: #fff;
}

.status-badge.status-active {
  background-color: rgba(40, 167, 69, 0.9);
  color: #fff;
}

.status-badge.status-used {
  background-color: rgba(108, 117, 125, 0.9);
  color: #fff;
}

.status-badge.status-expired {
  background-color: rgba(220, 53, 69, 0.9);
  color: #fff;
}

/* 门票内容 */
.coupon-content {
  margin-bottom: 40rpx;
}

/* 景区信息 */
.scenic-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.scenic-info:active {
  background-color: rgba(255, 255, 255, 0.2);
}

.scenic-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 20rpx;
  margin-left: -20rpx;
  flex-shrink: 0;
}

.scenic-img {
  width: 100%;
  height: 100%;
}

.scenic-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.scenic-name {
  font-size: 28rpx;
  color: #000;
  font-weight: 600;
}

.scenic-location-back {
  background-color: rgba(232,243,255,1.000000);
  display: flex;
  width: fit-content; /* 让容器宽度适应内容 */
  padding: 8rpx 10rpx; /* 可选：添加一些内边距 */
}
 
.scenic-location {
  font-size: 24rpx;
  color: rgba(64,128,255,1.000000);
  white-space: nowrap; /* 可选：防止文字换行 */
}

.scenic-arrow {
  margin-left: 20rpx;
  flex-shrink: 0;
}

.arrow-text {
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.4);
  font-weight: bold;
}

.coupon-desc {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.9);
  margin-bottom: 20rpx;
  display: block;
}

.coupon-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.info-item {
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.8);
}

/* 门票操作 */
.coupon-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
}

.action-left {
  flex: 1;
}

.rules-link {
  font-size: 26rpx;
  color: rgba(0, 0, 0, 0.8);
  text-decoration: underline;
}

.action-right {
  flex-shrink: 0;
}

.usage-status-btn {
  background-image: linear-gradient(91deg, #fda02e 0, rgb(230, 144, 47) 100.000000%);
  border-radius: 16rpx;
  height: 58rpx;
  display: flex;
  flex-direction: column;
  width: 174rpx;
  align-items: center;
  justify-content: center;
  color: #ffffff;
}

.share-btn {
  background-image: linear-gradient(91deg, rgb(114, 155, 245) 0, rgb(45, 172, 231) 100.000000%);
  border-radius: 50%;
  height: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  line-height: inherit;
  max-width: 60rpx;
  margin-top: 20rpx;
}

.use-btn {
  background-image: linear-gradient(91deg, rgba(238,116,53,1.000000) 0, rgba(236,87,91,1.000000) 100.000000%);
  border-radius: 16rpx;
  height: 58rpx;
  display: flex;
  flex-direction: column;
  width: 174rpx;
  align-items: center;
  justify-content: center;
}

.use-btn-text {
  color: rgba(255,255,255,1.000000);
}

.used-btn {
  background-color: rgba(108, 117, 125, 0.3);
  border-radius: 25rpx;
  padding: 16rpx 40rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.used-btn-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
}

.expired-btn {
  background-color: rgba(220, 53, 69, 0.3);
  border-radius: 25rpx;
  padding: 16rpx 40rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.expired-btn-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
}

/* 加载状态 */
.loading-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text,
.empty-text {
  font-size: 28rpx;
  color: #999;
}

.load-more,
.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80rpx;
  margin-top: 20rpx;
}

.load-more-text,
.no-more-text {
  font-size: 24rpx;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .coupon-item {
    padding: 30rpx 20rpx;
  }

  .title-text {
    font-size: 32rpx;
  }

  .coupon-desc {
    font-size: 26rpx;
  }
}

/* 使用情况弹窗样式 */
.usage-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.usage-modal-container {
  background-color: white;
  border-radius: 32rpx;
  width: 100%;
  max-width: 1200rpx;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.2);
}

.usage-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 40rpx 20rpx 40rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.usage-modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.usage-modal-close {
  width: 64rpx;
  height: 64rpx;
  border-radius: 32rpx;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.usage-modal-close-text {
  font-size: 40rpx;
  color: #666;
  line-height: 1;
}

.usage-modal-content {
  flex: 1;
  padding: 20rpx 40rpx;
  max-height: 50vh;
}

.usage-scenic-item {
  margin-bottom: 32rpx;
}

.usage-scenic-card {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
  max-width: 600rpx;
}

.usage-scenic-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(40rpx, -40rpx);
}

.usage-scenic-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.usage-scenic-title {
  color: black;
  font-size: 32rpx;
  font-weight: 600;
  flex: 1;
  margin-right: 24rpx;
}

.usage-status-badge {
  padding: 8rpx 24rpx;
  border-radius: 40rpx;
  font-size: 24rpx;
}

.usage-status-badge.unactivated {
  background-color: rgba(10, 8, 8, 0.2);
  border: 2rpx solid rgba(133, 133, 133, 0.3);
}

.usage-status-badge.active {
  background-color: #4CAF50;
  border: 2rpx solid #45a049;
}

.usage-status-badge.used {
  background-color: #FF9800;
  border: 2rpx solid #f57c00;
}

.usage-status-badge.expired {
  background-color: #f44336;
  border: 2rpx solid #d32f2f;
}

.usage-status-text {
  color: white;
  font-weight: 500;
}

.usage-scenic-address {
  margin-bottom: 24rpx;
}

.usage-address-text {
  color: black;
  font-size: 28rpx;
  line-height: 1.4;
}

.usage-scenic-details {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  padding: 24rpx;
  backdrop-filter: blur(10px);
}

.usage-detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.usage-detail-item:last-child {
  margin-bottom: 0;
}

.usage-detail-label {
  color: black;
  font-size: 26rpx;
  margin-right: 16rpx;
  min-width: 140rpx;
}

.usage-detail-value {
  color: black;
  font-size: 26rpx;
  font-weight: 500;
}

.usage-detail-placeholder {
  color: rgba(29, 20, 20, 0.7);
  font-size: 26rpx;
  font-style: italic;
}

.usage-modal-footer {
  padding: 30rpx 40rpx 40rpx 40rpx;
  border-top: 2rpx solid #f0f0f0;
}

.usage-modal-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.usage-modal-btn-text {
  color: white;
  font-size: 32rpx;
  font-weight: 500;
}
