const httpService = require('./httpService');

/**
 * 评价服务类
 * 提供评价相关的API调用功能
 */
class ReviewService {
  constructor() {
    this.baseUrl = '/api/reviews';
    this.cacheKeyPrefix = 'review_';
    this.cacheExpireTime = 5 * 60 * 1000; // 5分钟缓存
  }

  /**
   * 根据景区ID获取评价列表
   * @param {string} scenicId 景区ID
   * @param {Object} params 查询参数
   */
  async getScenicReviews(scenicId, params = {}) {
    try {
      if (!scenicId) {
        throw new Error('景区ID不能为空');
      }

      const {
        current = 1,
        size = 10
      } = params;

      // 构建缓存key
      const cacheKey = this.buildCacheKey('scenic_reviews', { scenicId, current, size });

      // 检查缓存
      const cachedData = this.getCache(cacheKey);
      if (cachedData) {
        console.log('使用缓存的景区评价数据');
        return cachedData;
      }

      console.log(`从服务器获取景区评价 (景区ID: ${scenicId})`);
      
      // 使用分页接口，通过scenicId参数筛选
      const queryParams = {
        scenicId: scenicId.toString(),
        current: current.toString(),
        size: size.toString(),
        status: '1'  // 只获取显示状态的评价
      };

      const result = await httpService.get(`${this.baseUrl}/page`, queryParams, {
        showLoading: false
      });

      // 处理返回数据格式
      let reviewsData;
      if (result && result.records) {
        // 分页数据格式
        reviewsData = {
          list: result.records || [],
          total: result.total || 0,
          current: result.current || 1,
          size: result.size || 10,
          pages: result.pages || 0
        };
      } else if (Array.isArray(result)) {
        // 数组格式
        reviewsData = {
          list: result,
          total: result.length,
          current: 1,
          size: result.length,
          pages: 1
        };
      } else {
        // 空数据
        reviewsData = {
          list: [],
          total: 0,
          current: 1,
          size: size,
          pages: 0
        };
      }

      // 缓存数据
      this.setCache(cacheKey, reviewsData);

      console.log('景区评价获取成功:', reviewsData);
      return reviewsData;
    } catch (error) {
      console.error('获取景区评价失败:', error);
      throw error;
    }
  }

  /**
   * 获取景区评价统计信息
   * @param {string} scenicId 景区ID
   */
  async getScenicReviewStats(scenicId) {
    try {
      if (!scenicId) {
        throw new Error('景区ID不能为空');
      }

      console.log(`获取景区评价统计 (景区ID: ${scenicId})`);

      const result = await httpService.get(`${this.baseUrl}/scenic/${scenicId}/stats`, {}, {
        showLoading: false
      });

      console.log('景区评价统计获取成功:', result);
      return result;
    } catch (error) {
      console.error('获取景区评价统计失败:', error);
      throw error;
    }
  }

  /**
   * 直接根据景区ID获取评价列表（不分页）
   * @param {string} scenicId 景区ID
   */
  async getReviewsByScenicId(scenicId) {
    try {
      if (!scenicId) {
        throw new Error('景区ID不能为空');
      }

      console.log(`获取景区所有评价 (景区ID: ${scenicId})`);

      const result = await httpService.get(`${this.baseUrl}/scenic/${scenicId}`, {}, {
        showLoading: false
      });

      // 处理返回数据格式
      let reviewsData;
      if (Array.isArray(result)) {
        reviewsData = {
          list: result,
          total: result.length,
          current: 1,
          size: result.length,
          pages: 1
        };
      } else {
        reviewsData = {
          list: [],
          total: 0,
          current: 1,
          size: 0,
          pages: 0
        };
      }

      console.log('景区所有评价获取成功:', reviewsData);
      return reviewsData;
    } catch (error) {
      console.error('获取景区所有评价失败:', error);
      throw error;
    }
  }

  /**
   * 构建缓存key
   * @param {string} type 缓存类型
   * @param {Object} params 参数对象
   */
  buildCacheKey(type, params) {
    const paramStr = Object.keys(params)
      .sort()
      .map(key => `${key}=${params[key]}`)
      .join('&');
    return `${this.cacheKeyPrefix}${type}_${paramStr}`;
  }

  /**
   * 获取缓存数据
   * @param {string} key 缓存key
   */
  getCache(key) {
    try {
      const cacheData = wx.getStorageSync(key);
      if (cacheData && cacheData.expireTime > Date.now()) {
        return cacheData.data;
      }
      // 缓存过期，删除
      if (cacheData) {
        wx.removeStorageSync(key);
      }
      return null;
    } catch (error) {
      console.error('获取缓存失败:', error);
      return null;
    }
  }

  /**
   * 设置缓存数据
   * @param {string} key 缓存key
   * @param {any} data 缓存数据
   */
  setCache(key, data) {
    try {
      const cacheData = {
        data: data,
        expireTime: Date.now() + this.cacheExpireTime
      };
      wx.setStorageSync(key, cacheData);
    } catch (error) {
      console.error('设置缓存失败:', error);
    }
  }

  /**
   * 清除缓存
   */
  clearCache() {
    try {
      const keys = wx.getStorageInfoSync().keys;
      keys.forEach(key => {
        if (key.startsWith(this.cacheKeyPrefix)) {
          wx.removeStorageSync(key);
        }
      });
      console.log('评价缓存已清除');
    } catch (error) {
      console.error('清除评价缓存失败:', error);
    }
  }
}

// 创建单例实例
const reviewService = new ReviewService();

module.exports = reviewService;
