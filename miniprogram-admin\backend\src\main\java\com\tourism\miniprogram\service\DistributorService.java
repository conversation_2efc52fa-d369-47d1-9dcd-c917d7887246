package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.entity.Distributor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 分销员服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
public interface DistributorService extends IService<Distributor> {

    /**
     * 根据用户ID获取分销员信息
     *
     * @param userId 用户ID
     * @return 分销员信息
     */
    Distributor getByUserId(Integer userId);

    /**
     * 根据分销员编号获取分销员信息
     *
     * @param distributorCode 分销员编号
     * @return 分销员信息
     */
    Distributor getByDistributorCode(String distributorCode);

    /**
     * 根据状态获取分销员列表
     *
     * @param status 状态
     * @return 分销员列表
     */
    List<Distributor> getByStatus(String status);

    /**
     * 创建分销员
     *
     * @param userId 用户ID
     * @param commissionRate 佣金比例
     * @return 分销员信息
     */
    Distributor createDistributor(Integer userId, BigDecimal commissionRate);

    /**
     * 更新分销员状态
     *
     * @param distributorId 分销员ID
     * @param status 新状态
     * @return 是否成功
     */
    boolean updateStatus(Integer distributorId, String status);

    /**
     * 更新分销员佣金信息
     *
     * @param distributorId 分销员ID
     * @param totalCommission 累积佣金总额
     * @param availableCommission 可提现金额
     * @param unsettledCommission 未结算金额
     * @return 是否成功
     */
    boolean updateCommissionInfo(Integer distributorId, BigDecimal totalCommission, 
                               BigDecimal availableCommission, BigDecimal unsettledCommission);

    /**
     * 更新分销员今日激活人数
     *
     * @param distributorId 分销员ID
     * @param todayActivated 今日激活人数
     * @return 是否成功
     */
    boolean updateTodayActivated(Integer distributorId, Integer todayActivated);

    /**
     * 增加分销员今日激活人数
     *
     * @param distributorId 分销员ID
     * @return 更新的记录数
     */
    int incrementTodayActivated(Integer distributorId);

    /**
     * 重置所有分销员今日激活人数（定时任务使用）
     *
     * @return 是否成功
     */
    boolean resetTodayActivated();

    /**
     * 生成分销员编号
     *
     * @return 分销员编号
     */
    String generateDistributorCode();

    /**
     * 生成分销员专属二维码
     *
     * @param distributorId 分销员ID
     * @return 二维码URL
     */
    String generateQrCode(Integer distributorId);

    /**
     * 获取活跃分销员数量
     *
     * @return 活跃分销员数量
     */
    Integer getActiveDistributorCount();

    /**
     * 获取分销员总佣金统计
     *
     * @return 总佣金金额
     */
    BigDecimal getTotalCommissionSum();

    /**
     * 检查用户是否已是分销员
     *
     * @param userId 用户ID
     * @return 是否是分销员
     */
    boolean isDistributor(Integer userId);

    /**
     * 生成分销员专属小程序码
     *
     * @param distributorId 分销员ID
     * @return 小程序码URL
     */
    String generateMiniProgramCode(Integer distributorId);

    /**
     * 冻结分销员
     *
     * @param distributorId 分销员ID
     * @param reason 冻结原因
     * @return 是否成功
     */
    boolean freezeDistributor(Integer distributorId, String reason);

    /**
     * 解冻分销员
     *
     * @param distributorId 分销员ID
     * @return 是否成功
     */
    boolean unfreezeDistributor(Integer distributorId);
}
