// app.js
App({
  onLaunch(options) {
    console.log('小程序启动，启动参数：', options)

    // 检测启动场景和参数
    this.checkLaunchScene(options)
  },

  onShow(options) {
    console.log('小程序显示，显示参数：', options)

    // 每次显示时也检测场景参数
    this.checkLaunchScene(options)
  },

  /**
   * 检测启动场景和二维码参数
   */
  checkLaunchScene(options) {
    console.log('检测启动场景，scene:', options.scene, 'query:', options.query)

    // 检测是否通过扫描二维码进入
    if (this.isFromQRCode(options.scene)) {
      console.log('通过二维码进入小程序')
      this.handleQRCodeParams(options)
    } else {
      console.log('正常启动小程序，scene:', options.scene)
    }
  },

  /**
   * 判断是否通过二维码进入
   */
  isFromQRCode(scene) {
    // 微信小程序二维码相关的场景值
    const qrCodeScenes = [
      1011, // 扫描二维码
      1012, // 长按识别二维码
      1013, // 手机相册选取二维码
      1047, // 扫描小程序码
      1048, // 长按识别小程序码
      1049  // 手机相册选取小程序码
    ]

    return qrCodeScenes.includes(scene)
  },

  /**
   * 处理二维码参数
   */
  handleQRCodeParams(options) {
    let distributorCode = null

    try {
      // 处理不同类型的参数
      if (options.query && options.query.scene) {
        // 从query.scene中解析参数
        distributorCode = this.parseDistributorFromScene(options.query.scene)
      } else if (options.scene && typeof options.scene === 'string') {
        // 直接从scene字符串中解析
        distributorCode = this.parseDistributorFromScene(options.scene)
      }

      if (distributorCode) {
        console.log('检测到推荐员编号:', distributorCode)
        this.saveDistributorInfo(distributorCode)
      } else {
        console.log('未检测到推荐员参数')
      }
    } catch (error) {
      console.error('解析二维码参数失败:', error)
    }
  },

  /**
   * 从场景值中解析推荐员编号
   */
  parseDistributorFromScene(scene) {
    if (!scene) return null

    try {
      // 解码URL编码的场景值
      const decodedScene = decodeURIComponent(scene)
      console.log('解码后的场景值:', decodedScene)

      // 解析推荐员参数：distributor=DIST12345678
      const distributorMatch = decodedScene.match(/distributor=([A-Z0-9]+)/i)
      if (distributorMatch && distributorMatch[1]) {
        return distributorMatch[1]
      }

      // 兼容其他可能的格式
      const codeMatch = decodedScene.match(/code=([A-Z0-9]+)/i)
      if (codeMatch && codeMatch[1]) {
        return codeMatch[1]
      }

      return null
    } catch (error) {
      console.error('解析场景值失败:', error)
      return null
    }
  },

  /**
   * 保存推荐员信息
   */
  saveDistributorInfo(distributorCode) {
    try {
      const distributorInfo = {
        code: distributorCode,
        timestamp: Date.now(),
        date: new Date().toISOString()
      }

      // 保存到全局数据
      this.globalData.distributorInfo = distributorInfo

      // 保存到本地存储
      wx.setStorageSync('distributorInfo', distributorInfo)

      console.log('推荐员信息已保存:', distributorInfo)

      // 触发自定义事件，通知页面推荐员信息已更新
      if (typeof this.onDistributorInfoUpdated === 'function') {
        this.onDistributorInfoUpdated(distributorInfo)
      }
    } catch (error) {
      console.error('保存推荐员信息失败:', error)
    }
  },

  /**
   * 获取推荐员信息
   */
  getDistributorInfo() {
    // 优先从全局数据获取
    if (this.globalData.distributorInfo) {
      return this.globalData.distributorInfo
    }

    // 从本地存储获取
    try {
      const distributorInfo = wx.getStorageSync('distributorInfo')
      if (distributorInfo) {
        this.globalData.distributorInfo = distributorInfo
        return distributorInfo
      }
    } catch (error) {
      console.error('获取本地推荐员信息失败:', error)
    }

    return null
  },

  /**
   * 清除推荐员信息
   */
  clearDistributorInfo() {
    try {
      this.globalData.distributorInfo = null
      wx.removeStorageSync('distributorInfo')
      console.log('推荐员信息已清除')
    } catch (error) {
      console.error('清除推荐员信息失败:', error)
    }
  },

  /**
   * 检查推荐员信息是否有效（可设置过期时间）
   */
  isDistributorInfoValid(expireHours) {
    const distributorInfo = this.getDistributorInfo()
    if (!distributorInfo) return false

    const now = Date.now()
    const expireTime = distributorInfo.timestamp + (expireHours * 60 * 60 * 1000)

    return now < expireTime
  },

  globalData: {
    userInfo: null,
    baseUrl: 'http://localhost:8080',
    distributorInfo: 'DIST384440093646' // 推荐员信息
  }
})
