/**
 * 推荐员相关工具函数
 */

/**
 * 获取当前推荐员信息
 */
function getDistributorInfo() {
  const app = getApp()
  return app.getDistributorInfo()
}

/**
 * 检查是否有推荐员信息
 */
function hasDistributorInfo() {
  const distributorInfo = getDistributorInfo()
  return distributorInfo && distributorInfo.code
}

/**
 * 获取推荐员编号
 */
function getDistributorCode() {
  const distributorInfo = getDistributorInfo()
  return distributorInfo ? distributorInfo.code : null
}

/**
 * 检查推荐员信息是否有效
 */
function isDistributorInfoValid(expireHours) {
  const app = getApp()
  return app.isDistributorInfoValid(expireHours)
}

/**
 * 清除推荐员信息
 */
function clearDistributorInfo() {
  const app = getApp()
  app.clearDistributorInfo()
}

/**
 * 在订单中使用推荐员信息
 */
function getOrderDistributorParams() {
  const distributorInfo = getDistributorInfo()
  if (!distributorInfo || !isDistributorInfoValid()) {
    return {}
  }
  
  return {
    distributorCode: distributorInfo.code,
    distributorTimestamp: distributorInfo.timestamp
  }
}

/**
 * 显示推荐员信息（调试用）
 */
function showDistributorInfo() {
  const distributorInfo = getDistributorInfo()
  if (distributorInfo) {
    wx.showModal({
      title: '推荐员信息',
      content: `编号: ${distributorInfo.code}\n时间: ${new Date(distributorInfo.timestamp).toLocaleString()}`,
      showCancel: false
    })
  } else {
    wx.showToast({
      title: '无推荐员信息',
      icon: 'none'
    })
  }
}

module.exports = {
  getDistributorInfo,
  hasDistributorInfo,
  getDistributorCode,
  isDistributorInfoValid,
  clearDistributorInfo,
  getOrderDistributorParams,
  showDistributorInfo
}
