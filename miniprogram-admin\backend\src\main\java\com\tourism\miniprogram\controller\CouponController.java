package com.tourism.miniprogram.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.entity.Coupon;
import com.tourism.miniprogram.service.CouponService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 数字门票控制器
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@RestController
@RequestMapping("/coupons")
@Api(tags = "数字门票管理")
public class CouponController {

    @Autowired
    private CouponService couponService;

    /**
     * 分页获取门票列表
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页获取门票列表", notes = "分页获取门票列表，支持按编码、用户ID、状态筛选")
    public Result<IPage<Coupon>> getCouponPage(
            @ApiParam(value = "当前页", defaultValue = "1") @RequestParam(defaultValue = "1") Long current,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") Long size,
            @ApiParam(value = "卡券编码") @RequestParam(required = false) String code,
            @ApiParam(value = "用户ID") @RequestParam(required = false) Integer userId,
            @ApiParam(value = "状态") @RequestParam(required = false) String status) {
        try {
            Page<Coupon> page = new Page<>(current, size);
            QueryWrapper<Coupon> queryWrapper = new QueryWrapper<>();

            if (StringUtils.hasText(code)) {
                queryWrapper.like("code", code);
            }
            if (userId != null) {
                queryWrapper.eq("user_id", userId);
            }
            if (StringUtils.hasText(status)) {
                queryWrapper.eq("status", status);
            }
            queryWrapper.orderByDesc("id");

            IPage<Coupon> couponPage = couponService.page(page, queryWrapper);
            return Result.success(couponPage);
        } catch (Exception e) {
            log.error("分页获取门票列表失败", e);
            return Result.error("获取门票列表失败");
        }
    }

    /**
     * 获取门票列表
     */
    @GetMapping
    @ApiOperation(value = "获取门票列表", notes = "获取所有门票列表")
    public Result<List<Coupon>> getCoupons() {
        try {
            QueryWrapper<Coupon> queryWrapper = new QueryWrapper<>();
            queryWrapper.orderByDesc("id");
            List<Coupon> coupons = couponService.list(queryWrapper);
            return Result.success(coupons);
        } catch (Exception e) {
            log.error("获取门票列表失败", e);
            return Result.error("获取门票列表失败");
        }
    }

    /**
     * 获取门票详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取门票详情", notes = "根据ID获取门票详情")
    public Result<Coupon> getCouponById(@ApiParam(value = "门票ID", required = true) @PathVariable Integer id) {
        try {
            Coupon coupon = couponService.getById(id);
            if (coupon == null) {
                return Result.error(404, "门票不存在");
            }
            return Result.success(coupon);
        } catch (Exception e) {
            log.error("获取门票详情失败，id: {}", id, e);
            return Result.error("获取门票详情失败");
        }
    }

    /**
     * 根据编码获取门票
     */
    @GetMapping("/code/{code}")
    @ApiOperation(value = "根据编码获取门票", notes = "根据卡券编码获取门票信息")
    public Result<Coupon> getCouponByCode(@ApiParam(value = "卡券编码", required = true) @PathVariable String code) {
        try {
            QueryWrapper<Coupon> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("code", code);
            Coupon coupon = couponService.getOne(queryWrapper);
            if (coupon == null) {
                return Result.error(404, "门票不存在");
            }
            return Result.success(coupon);
        } catch (Exception e) {
            log.error("根据编码获取门票失败，code: {}", code, e);
            return Result.error("获取门票失败");
        }
    }

    /**
     * 根据订单ID获取门票列表
     */
    @GetMapping("/order/{orderId}")
    @ApiOperation(value = "根据订单ID获取门票", notes = "根据订单ID获取该订单关联的所有门票")
    public Result<List<Coupon>> getCouponsByOrderId(@ApiParam(value = "订单ID", required = true) @PathVariable Integer orderId) {
        try {
            List<Coupon> coupons = couponService.getCouponsByOrderId(orderId);
            return Result.success(coupons);
        } catch (Exception e) {
            log.error("根据订单ID获取门票失败，orderId: {}", orderId, e);
            return Result.error("获取门票失败");
        }
    }

    /**
     * 创建门票
     */
    @PostMapping
    @ApiOperation(value = "创建门票", notes = "创建新的门票")
    public Result<Coupon> createCoupon(@RequestBody @Valid Coupon coupon) {
        try {
            boolean success = couponService.save(coupon);
            if (success) {
                // 重新查询获取完整的门票信息（包含自动生成的ID和时间戳）
                Coupon createdCoupon = couponService.getById(coupon.getId());
                return Result.success(createdCoupon);
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建门票失败", e);
            return Result.error("创建门票失败");
        }
    }

    /**
     * 更新门票
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "更新门票", notes = "根据ID更新门票信息")
    public Result<String> updateCoupon(
            @ApiParam(value = "门票ID", required = true) @PathVariable Integer id,
            @RequestBody @Valid Coupon coupon) {
        try {
            coupon.setId(id);
            boolean success = couponService.updateById(coupon);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新门票失败，id: {}", id, e);
            return Result.error("更新门票失败");
        }
    }

    /**
     * 删除门票
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除门票", notes = "根据ID删除门票")
    public Result<String> deleteCoupon(@ApiParam(value = "门票ID", required = true) @PathVariable Integer id) {
        try {
            boolean success = couponService.removeById(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除门票失败，id: {}", id, e);
            return Result.error("删除门票失败");
        }
    }

    /**
     * 根据用户ID和景区ID查询未激活的数字门票
     * 支持单品和组合包产品
     */
    @GetMapping("/unactivated")
    @ApiOperation(value = "查询未激活门票", notes = "根据用户ID和景区ID查询未激活的数字门票列表，支持单品和组合包产品")
    public Result<List<Coupon>> getUnactivatedCoupons(
            @ApiParam(value = "用户ID", required = true) @RequestParam Integer userId,
            @ApiParam(value = "景区ID", required = true) @RequestParam String scenicId) {
        try {
            log.info("查询未激活门票（支持组合包），userId: {}, scenicId: {}", userId, scenicId);

            // 参数验证
            if (userId == null) {
                return Result.error("用户ID不能为空");
            }
            if (scenicId == null || scenicId.trim().isEmpty()) {
                return Result.error("景区ID不能为空");
            }

            List<Coupon> coupons = couponService.getUnactivatedCouponsByUserAndScenic(userId, scenicId);
            log.info("查询到未激活门票数量: {}", coupons.size());
            return Result.success(coupons);
        } catch (Exception e) {
            log.error("查询未激活门票失败，userId: {}, scenicId: {}", userId, scenicId, e);
            return Result.error("查询未激活门票失败: " + e.getMessage());
        }
    }

    /**
     * 检查用户在指定景区是否有未激活的数字门票
     * 支持单品和组合包产品
     */
    @GetMapping("/has-unactivated")
    @ApiOperation(value = "检查是否有未激活门票", notes = "检查用户在指定景区是否有未激活的数字门票，支持单品和组合包产品")
    public Result<Boolean> hasUnactivatedCoupons(
            @ApiParam(value = "用户ID", required = true) @RequestParam Integer userId,
            @ApiParam(value = "景区ID", required = true) @RequestParam String scenicId) {
        try {
            log.info("检查是否有未激活门票（支持组合包），userId: {}, scenicId: {}", userId, scenicId);

            // 参数验证
            if (userId == null) {
                return Result.error("用户ID不能为空");
            }
            if (scenicId == null || scenicId.trim().isEmpty()) {
                return Result.error("景区ID不能为空");
            }

            boolean hasUnactivated = couponService.hasUnactivatedCoupons(userId, scenicId);
            log.info("检查结果: {}", hasUnactivated);
            return Result.success(hasUnactivated);
        } catch (Exception e) {
            log.error("检查未激活门票失败，userId: {}, scenicId: {}", userId, scenicId, e);
            return Result.error("检查未激活门票失败: " + e.getMessage());
        }
    }

    /**
     * 根据用户ID和产品ID查询未激活的数字门票
     */
    @GetMapping("/unactivated-by-product")
    @ApiOperation(value = "按产品ID查询未激活门票", notes = "根据用户ID和产品ID查询未激活的数字门票列表")
    public Result<List<Coupon>> getUnactivatedCouponsByProduct(
            @ApiParam(value = "用户ID", required = true) @RequestParam Integer userId,
            @ApiParam(value = "产品ID", required = true) @RequestParam String productId) {
        try {
            log.info("查询未激活门票（按产品ID），userId: {}, productId: {}", userId, productId);

            // 参数验证
            if (userId == null) {
                return Result.error("用户ID不能为空");
            }
            if (productId == null || productId.trim().isEmpty()) {
                return Result.error("产品ID不能为空");
            }

            List<Coupon> coupons = couponService.getUnactivatedCouponsByUserAndProduct(userId, productId);
            log.info("查询到未激活门票数量: {}", coupons.size());
            return Result.success(coupons);
        } catch (Exception e) {
            log.error("查询未激活门票失败，userId: {}, productId: {}", userId, productId, e);
            return Result.error("查询未激活门票失败: " + e.getMessage());
        }
    }

    /**
     * 检查用户在指定产品是否有未激活的数字门票
     */
    @GetMapping("/has-unactivated-by-product")
    @ApiOperation(value = "按产品ID检查是否有未激活门票", notes = "检查用户在指定产品是否有未激活的数字门票")
    public Result<Boolean> hasUnactivatedCouponsByProduct(
            @ApiParam(value = "用户ID", required = true) @RequestParam Integer userId,
            @ApiParam(value = "产品ID", required = true) @RequestParam String productId) {
        try {
            log.info("检查是否有未激活门票（按产品ID），userId: {}, productId: {}", userId, productId);

            // 参数验证
            if (userId == null) {
                return Result.error("用户ID不能为空");
            }
            if (productId == null || productId.trim().isEmpty()) {
                return Result.error("产品ID不能为空");
            }

            boolean hasUnactivated = couponService.hasUnactivatedCouponsByProduct(userId, productId);
            log.info("检查结果: {}", hasUnactivated);
            return Result.success(hasUnactivated);
        } catch (Exception e) {
            log.error("检查未激活门票失败，userId: {}, productId: {}", userId, productId, e);
            return Result.error("检查未激活门票失败: " + e.getMessage());
        }
    }

    /**
     * 根据用户ID和景区ID查询已激活的数字门票
     */
    @GetMapping("/activated")
    @ApiOperation(value = "查询已激活门票", notes = "根据用户ID和景区ID查询已激活的数字门票列表")
    public Result<List<Coupon>> getActivatedCoupons(
            @ApiParam(value = "用户ID", required = true) @RequestParam Integer userId,
            @ApiParam(value = "景区ID", required = true) @RequestParam String scenicId) {
        try {
            log.info("查询已激活门票，userId: {}, scenicId: {}", userId, scenicId);

            // 参数验证
            if (userId == null) {
                return Result.error("用户ID不能为空");
            }
            if (scenicId == null || scenicId.trim().isEmpty()) {
                return Result.error("景区ID不能为空");
            }

            List<Coupon> coupons = couponService.getActivatedCouponsByUserAndScenic(userId, scenicId);
            log.info("查询到已激活门票数量: {}", coupons.size());
            return Result.success(coupons);
        } catch (Exception e) {
            log.error("查询已激活门票失败，userId: {}, scenicId: {}", userId, scenicId, e);
            return Result.error("查询已激活门票失败: " + e.getMessage());
        }
    }

    /**
     * 检查用户在指定景区是否有已激活的数字门票
     */
    @GetMapping("/has-activated")
    @ApiOperation(value = "检查是否有已激活门票", notes = "检查用户在指定景区是否有已激活的数字门票")
    public Result<Boolean> hasActivatedCoupons(
            @ApiParam(value = "用户ID", required = true) @RequestParam Integer userId,
            @ApiParam(value = "景区ID", required = true) @RequestParam String scenicId) {
        try {
            log.info("检查是否有已激活门票，userId: {}, scenicId: {}", userId, scenicId);

            // 参数验证
            if (userId == null) {
                return Result.error("用户ID不能为空");
            }
            if (scenicId == null || scenicId.trim().isEmpty()) {
                return Result.error("景区ID不能为空");
            }

            boolean hasActivated = couponService.hasActivatedCoupons(userId, scenicId);
            log.info("检查结果: {}", hasActivated);
            return Result.success(hasActivated);
        } catch (Exception e) {
            log.error("检查已激活门票失败，userId: {}, scenicId: {}", userId, scenicId, e);
            return Result.error("检查已激活门票失败: " + e.getMessage());
        }
    }

    /**
     * 根据用户ID和产品ID查询已激活的数字门票
     */
    @GetMapping("/activated-by-product")
    @ApiOperation(value = "按产品ID查询已激活门票", notes = "根据用户ID和产品ID查询已激活的数字门票列表")
    public Result<List<Coupon>> getActivatedCouponsByProduct(
            @ApiParam(value = "用户ID", required = true) @RequestParam Integer userId,
            @ApiParam(value = "产品ID", required = true) @RequestParam String productId) {
        try {
            log.info("查询已激活门票（按产品ID），userId: {}, productId: {}", userId, productId);

            // 参数验证
            if (userId == null) {
                return Result.error("用户ID不能为空");
            }
            if (productId == null || productId.trim().isEmpty()) {
                return Result.error("产品ID不能为空");
            }

            List<Coupon> coupons = couponService.getActivatedCouponsByUserAndProduct(userId, productId);
            log.info("查询到已激活门票数量: {}", coupons.size());
            return Result.success(coupons);
        } catch (Exception e) {
            log.error("查询已激活门票失败，userId: {}, productId: {}", userId, productId, e);
            return Result.error("查询已激活门票失败: " + e.getMessage());
        }
    }

    /**
     * 检查用户在指定产品是否有已激活的数字门票
     */
    @GetMapping("/has-activated-by-product")
    @ApiOperation(value = "按产品ID检查是否有已激活门票", notes = "检查用户在指定产品是否有已激活的数字门票")
    public Result<Boolean> hasActivatedCouponsByProduct(
            @ApiParam(value = "用户ID", required = true) @RequestParam Integer userId,
            @ApiParam(value = "产品ID", required = true) @RequestParam String productId) {
        try {
            log.info("检查是否有已激活门票（按产品ID），userId: {}, productId: {}", userId, productId);

            // 参数验证
            if (userId == null) {
                return Result.error("用户ID不能为空");
            }
            if (productId == null || productId.trim().isEmpty()) {
                return Result.error("产品ID不能为空");
            }

            boolean hasActivated = couponService.hasActivatedCouponsByProduct(userId, productId);
            log.info("检查结果: {}", hasActivated);
            return Result.success(hasActivated);
        } catch (Exception e) {
            log.error("检查已激活门票失败，userId: {}, productId: {}", userId, productId, e);
            return Result.error("检查已激活门票失败: " + e.getMessage());
        }
    }

    /**
     * 激活卡券
     */
    @PostMapping("/{couponId}/activate")
    @ApiOperation(value = "激活卡券", notes = "根据卡券ID激活指定的卡券")
    public Result<Coupon> activateCoupon(
            @ApiParam(value = "卡券ID", required = true) @PathVariable Integer couponId) {
        try {
            log.info("激活卡券，couponId: {}", couponId);

            // 参数验证
            if (couponId == null) {
                return Result.error(400, "卡券ID不能为空");
            }

            // 检查卡券是否存在
            Coupon coupon = couponService.getById(couponId);
            if (coupon == null) {
                return Result.error(404, "卡券不存在");
            }

            // 检查卡券状态
            if (!"unactivated".equals(coupon.getStatus())) {
                return Result.error(400, "卡券状态不允许激活，当前状态：" + coupon.getStatus());
            }

            // 激活卡券
            boolean success = couponService.activateCouponDirect(couponId);
            if (success) {
                // 重新查询更新后的卡券信息
                Coupon updatedCoupon = couponService.getById(couponId);
                log.info("卡券激活成功，couponId: {}", couponId);
                return Result.success("卡券激活成功", updatedCoupon);
            } else {
                log.error("卡券激活失败，couponId: {}", couponId);
                return Result.error(500, "卡券激活失败");
            }

        } catch (RuntimeException e) {
            log.error("激活卡券失败，couponId: {}", couponId, e);
            return Result.error(500, e.getMessage());
        } catch (Exception e) {
            log.error("激活卡券系统异常，couponId: {}", couponId, e);
            return Result.error(500, "系统异常，请稍后重试");
        }
    }
}
