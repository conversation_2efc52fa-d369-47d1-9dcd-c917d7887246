const apiService = require('../../utils/apiService');
const distributorUtil = require('../../utils/distributor');

Page({
  data: {
    // 页面参数
    productId: '',
    bundleId: '', // 组合包ID
    scenicId: '', // 从产品详情中获取
    orderType: 'product', // 订单类型：product(单品) 或 bundle(组合包)

    // 详情数据
    productDetail: null,
    bundleDetail: null,
    carouselDetail: null,
    scenicDetail: null,

    // 产品信息（用于显示）
    productInfo: {
      title: '',
      description: '',
      price: 0,
      image: '',
      duration: '',
      pointCount: 0
    },

    // 景区信息（用于显示）
    scenicInfo: {
      title: '',
      description: '',
      address: '',
      image: ''
    },

    // 组合包景区列表
    bundleScenicList: [],
    // 显示的景区列表（只显示前两个）
    displayScenicList: [],
    // 是否显示查看更多按钮
    showMoreButton: false,
    // 弹窗状态
    showScenicModal: false,

    // 购买相关
    quantity: 1,
    maxQuantity: 99,
    totalAmount: 0,

    // 用户信息
    userInfo: null,
    userId: null,

    // 页面状态
    loading: true,
    loadingText: '加载中...',
    error: false,
    errorMessage: '',
    processing: false
  },

  onLoad: function(options) {
    console.log('订单确认页面加载');
    console.log('接收到的页面参数:', options);

    // 解析页面参数
    const orderType = options.bundleId ? 'bundle' : 'product';
    this.setData({
      productId: options.productId || '',
      bundleId: options.bundleId || '',
      orderType: orderType
    });

    // 初始化页面数据
    this.initPageData();
  },

  // 初始化页面数据
  initPageData: async function() {
    try {
      this.setData({
        loading: true,
        loadingText: '加载订单信息...',
        error: false
      });

      // 检查用户登录状态
      const loginStatus = await apiService.checkLoginStatus();
      console.log('订单确认页面 - 登录状态检查结果:', loginStatus);

      if (!loginStatus.isLoggedIn) {
        console.log('用户未登录，显示登录提示');
        // 用户未登录，跳转到登录页面
        wx.showModal({
          title: '提示',
          content: '请先登录后再购买',
          confirmText: '去登录',
          success: (res) => {
            if (res.confirm) {
              wx.switchTab({
                url: '/pages/lanhu_wode/component'
              });
            } else {
              wx.navigateBack();
            }
          }
        });
        return;
      }

      // 提取用户ID
      const userId = loginStatus.userInfo.id || loginStatus.userInfo.userId || loginStatus.userInfo.openid;
      if (!userId) {
        console.error('用户信息中缺少有效的用户ID:', loginStatus.userInfo);
        throw new Error('用户信息不完整，请重新登录');
      }

      // 设置用户信息
      this.setData({
        userInfo: loginStatus.userInfo,
        userId: userId
      });

      console.log('用户登录验证通过，用户ID:', userId);

      // 根据订单类型加载不同的数据
      if (this.data.orderType === 'bundle') {
        // 组合包订单
        if (!this.data.bundleId) {
          throw new Error('组合包ID不能为空');
        }
        await this.loadBundleDetail();
      } else {
        // 单品订单
        if (!this.data.productId) {
          throw new Error('讲解产品ID不能为空');
        }
        await this.loadProductDetail();

        // 从产品详情中获取景区ID，然后加载景区详情
        if (this.data.scenicId) {
          await this.loadScenicDetail();
        }
      }

      console.log('订单信息初始化完成');

    } catch (error) {
      console.error('初始化页面数据失败:', error);
      this.setData({
        loading: false,
        error: true,
        errorMessage: error.message || '加载失败，请重试'
      });
    }
  },

  // 加载组合包详情
  loadBundleDetail: async function() {
    try {
      console.log('加载组合包详情:', this.data.bundleId);

      this.setData({
        loadingText: '加载组合包信息...'
      });

      const bundleDetail = await apiService.getProductBundleDetail(this.data.bundleId);
      console.log('组合包详情加载成功:', bundleDetail);

      if (!bundleDetail) {
        throw new Error('未找到组合包信息');
      }

      // 映射组合包详情到显示信息
      const productInfo = {
        title: bundleDetail.name || '未知组合包',
        description: bundleDetail.description || '',
        price: parseFloat(bundleDetail.discount || 0),
        duration: '', // 组合包不显示时长
        pointCount: 0 // 组合包不显示讲解点
      };

      // 计算总金额
      const totalAmount = productInfo.price * this.data.quantity;

      this.setData({
        bundleDetail: bundleDetail,
        productInfo: productInfo,
        totalAmount: totalAmount
      });

      // 加载组合包包含的景区信息
      if (bundleDetail.scenicIds && Array.isArray(bundleDetail.scenicIds) && bundleDetail.scenicIds.length > 0) {
        console.log('开始加载组合包景区信息:', bundleDetail.scenicIds);
        await this.loadBundleScenicDetails(bundleDetail.scenicIds);
      } else {
        console.warn('组合包中没有景区ID信息');
        this.setData({
          loading: false
        });
      }

      console.log('组合包详情处理完成');

    } catch (error) {
      console.error('加载组合包详情失败:', error);
      throw new Error('加载组合包信息失败：' + (error.message || '未知错误'));
    }
  },

  // 批量加载组合包景区详情
  loadBundleScenicDetails: async function(scenicIds) {
    try {
      console.log('批量加载景区详情:', scenicIds);

      this.setData({
        loadingText: '加载景区信息...'
      });

      const scenicList = [];

      // 并发加载所有景区详情
      const scenicPromises = scenicIds.map(async (scenicId) => {
        try {
          const scenicDetail = await apiService.getScenicDetail(scenicId);
          if (scenicDetail) {
            return {
              id: scenicId,
              title: scenicDetail.title || scenicDetail.name || '未知景区',
              description: scenicDetail.description || scenicDetail.subtitle || '',
              address: scenicDetail.address || '地址信息暂无',
              image: scenicDetail.image || scenicDetail.images?.[0] || '',
              province: scenicDetail.province || '',
              city: scenicDetail.city || '',
              originalData: scenicDetail
            };
          }
          return null;
        } catch (error) {
          console.error(`加载景区详情失败 (ID: ${scenicId}):`, error);
          return {
            id: scenicId,
            title: '景区信息加载失败',
            description: '',
            address: '',
            image: '',
            province: '',
            city: '',
            error: true
          };
        }
      });

      const results = await Promise.all(scenicPromises);

      // 过滤掉null值并添加到景区列表
      results.forEach(scenic => {
        if (scenic) {
          scenicList.push(scenic);
        }
      });

      console.log('景区详情批量加载完成:', scenicList);

      // 处理显示逻辑：只显示前两个景区
      const displayScenicList = scenicList.slice(0, 2);
      const showMoreButton = scenicList.length > 2;

      this.setData({
        bundleScenicList: scenicList,
        displayScenicList: displayScenicList,
        showMoreButton: showMoreButton,
        loading: false
      });

      console.log('组合包景区信息加载完成，总数量:', scenicList.length, '显示数量:', displayScenicList.length);

    } catch (error) {
      console.error('批量加载景区详情失败:', error);
      this.setData({
        bundleScenicList: [],
        loading: false
      });
    }
  },

  // 加载景区详情
  loadScenicDetail: async function() {
    try {
      console.log('加载景区详情:', this.data.scenicId);

      this.setData({
        loadingText: '加载景区信息...'
      });

      const scenicDetail = await apiService.getScenicDetail(this.data.scenicId);
      console.log('景区详情加载成功:', scenicDetail);

      // 映射景区详情到显示信息（主要用于地址等补充信息）
      const scenicInfo = {
        title: scenicDetail.title || scenicDetail.name || '未知景区',
        description: scenicDetail.description || scenicDetail.subtitle || '',
        address: scenicDetail.address || '地址信息暂无',
        image: scenicDetail.image || scenicDetail.images?.[0] || ''
      };

      this.setData({
        scenicDetail: scenicDetail,
        scenicInfo: scenicInfo,
        loading: false
      });

      console.log('景区详情处理完成');

    } catch (error) {
      console.error('加载景区详情失败:', error);
      // 景区详情加载失败不影响主流程，只记录错误
      console.warn('景区详情加载失败，将只显示产品基础信息');
      this.setData({
        loading: false
      });
    }
  },

  // 加载产品详情
  loadProductDetail: async function() {
    try {
      console.log('加载讲解产品详情:', this.data.productId);

      this.setData({
        loadingText: '加载产品信息...'
      });

      const productDetail = await apiService.getGuideProductDetail(this.data.productId);
      console.log('讲解产品详情加载成功:', productDetail);

      // 从产品详情中提取景区ID
      const scenicId = productDetail.scenicId;
      if (!scenicId) {
        throw new Error('产品中未找到关联的景区ID');
      }

      // 映射产品详情到显示信息
      const productInfo = {
        title: productDetail.title || '未知产品',
        description: productDetail.description || '',
        price: parseFloat(productDetail.price || 0),
        image: productDetail.backgroundImageUrl || productDetail.startListeningImageUrl || '',
        duration: productDetail.duration || '',
        pointCount: productDetail.pointCount || 0
      };

      // 计算总金额
      const totalAmount = productInfo.price * this.data.quantity;

      this.setData({
        productDetail: productDetail,
        productInfo: productInfo,
        scenicId: scenicId,
        totalAmount: totalAmount
      });

      console.log('产品详情处理完成，景区ID:', scenicId);

    } catch (error) {
      console.error('加载讲解产品详情失败:', error);
      throw new Error('加载产品信息失败：' + (error.message || '未知错误'));
    }
  },

  // 重试加载
  onRetryLoad: function() {
    this.initPageData();
  },

  // 增加数量
  onIncreaseQuantity: function() {
    if (this.data.quantity >= this.data.maxQuantity) {
      wx.showToast({
        title: `最多购买${this.data.maxQuantity}张`,
        icon: 'none'
      });
      return;
    }

    const newQuantity = this.data.quantity + 1;
    const totalAmount = this.data.productInfo.price * newQuantity;

    this.setData({
      quantity: newQuantity,
      totalAmount: totalAmount
    });
  },

  // 减少数量
  onDecreaseQuantity: function() {
    if (this.data.quantity <= 1) {
      return;
    }

    const newQuantity = this.data.quantity - 1;
    const totalAmount = this.data.productInfo.price * newQuantity;

    this.setData({
      quantity: newQuantity,
      totalAmount: totalAmount
    });
  },

  // 支付处理
  onPayment: async function() {
    if (this.data.processing) {
      return;
    }

    try {
      this.setData({
        processing: true,
        loadingText: '处理支付中...'
      });

      console.log('开始支付流程');

      // 第一步：创建订单
      const orderData = {
        userId: this.data.userId,
        totalAmount: this.data.totalAmount,
        scenicId: this.data.scenicId,
        quantity: this.data.quantity,
        productName: this.data.productInfo.title,
        productPrice: this.data.productInfo.price
      };

      // 根据订单类型添加不同的参数
      if (this.data.orderType === 'bundle') {
        orderData.bundleId = this.data.bundleId;
        orderData.orderType = 'bundle';
        // 组合包订单使用组合包名称
        orderData.productName = this.data.bundleDetail ? this.data.bundleDetail.name : this.data.productInfo.title;
      } else {
        orderData.productId = this.data.productId;
        orderData.orderType = 'product';
      }

      console.log('创建订单:', orderData);
      const order = await apiService.createOrder(orderData);
      console.log('订单创建成功:', order);

      // 第二步：处理支付
      const paymentData = {
        amount: this.data.totalAmount,
        orderId: order.id,
        productName: this.data.orderType === 'bundle'
          ? (this.data.bundleDetail ? this.data.bundleDetail.name : this.data.productInfo.title)
          : (this.data.scenicInfo ? this.data.scenicInfo.title : this.data.productInfo.title)
      };

      console.log('处理支付:', paymentData);
      const paymentResult = await apiService.processPayment(order.id, paymentData);
      console.log('支付处理完成:', paymentResult);

      if (!paymentResult.success) {
        throw new Error('支付失败');
      }

      // 第三步：更新订单状态为已支付
      console.log('更新订单状态为已支付');
      await apiService.updateOrderStatus(order.id || order.orderId, 'paid');

      // 第四步：根据订单类型创建产品和门票
      if (this.data.orderType === 'bundle') {
        // 组合包订单：为每个景区创建产品和门票
        await this.createBundleProductsAndCoupons(order);
      } else {
        // 单品订单：创建单个产品和门票
        await this.createSingleProductAndCoupons(order);
      }

      // 第五步：检测推荐员信息并创建推荐订单
      await this.handleDistributorOrder(order);

      // 支付成功，显示成功提示
      wx.showToast({
        title: '购买成功！',
        icon: 'success',
        duration: 2000
      });

      // 延迟跳转到订单详情或我的订单页面
      setTimeout(() => {
        wx.redirectTo({
          url: `/pages/lanhu_quanbudingdan/component?orderId=${order.id || order.orderId}`
        });
      }, 2000);

    } catch (error) {
      console.error('支付流程失败:', error);

      this.setData({
        processing: false
      });

      wx.showModal({
        title: '支付失败',
        content: error.message || '支付过程中出现错误，请重试',
        showCancel: false,
        confirmText: '确定'
      });
    }
  },

  // 为组合包创建产品和门票
  createBundleProductsAndCoupons: async function(order) {
    try {
      console.log('开始为组合包创建产品和门票');

      if (!this.data.bundleScenicList || this.data.bundleScenicList.length === 0) {
        throw new Error('组合包景区列表为空');
      }

      // 将所有景区ID用逗号分隔组合成字符串
      const scenicIds = this.data.bundleScenicList.map(scenic => scenic.id).join(',');
      console.log('组合包包含的景区ID:', scenicIds);

      // 使用组合包名称作为产品名称
      const bundleName = this.data.bundleDetail ? this.data.bundleDetail.name : this.data.productInfo.title;

      // 创建单个组合包产品
      const productData = {
        name: bundleName,
        description: this.data.productInfo.description,
        price: this.data.productInfo.price,
        scenicId: scenicIds, // 将所有景区ID用逗号分隔保存
        orderId: order.id || order.orderId,
        productType: 'bundle',
        bundleId: this.data.bundleId,
        status: 1,
        requireActivation: true,
        validityHours: 24,
        image: this.data.productInfo.image
      };

      console.log('创建组合包产品:', productData);
      const product = await apiService.createProduct(productData);
      console.log('组合包产品创建成功:', product);

      // 根据购买数量创建对应数量的卡券
      const couponOrderData = {
        orderId: order.id || order.orderId,
        productId: product.id || product.productId,
        userId: this.data.userId,
        productName: bundleName,
        value: this.data.productInfo.price
      };

      console.log('为组合包批量创建卡券:', couponOrderData, this.data.quantity);
      const coupons = await apiService.createMultipleCoupons(couponOrderData, this.data.quantity);
      console.log('组合包卡券创建成功，数量:', coupons.length);

      // 为每张卡券创建使用记录
      for (let i = 0; i < coupons.length; i++) {
        const coupon = coupons[i];
        const couponId = coupon.id || coupon.couponId;

        console.log(`为卡券 ${couponId} 创建使用记录`);

        try {
          await this.createCouponUsageRecords(couponId, this.data.userId, this.data.bundleScenicList);
          console.log(`卡券 ${couponId} 使用记录创建成功`);
        } catch (error) {
          console.error(`卡券 ${couponId} 使用记录创建失败:`, error);
          // 不抛出错误，避免影响整个下单流程
        }
      }

      console.log('组合包产品和门票创建完成');
      console.log('创建的产品:', product);
      console.log('创建的卡券数量:', coupons.length);

    } catch (error) {
      console.error('创建组合包产品和门票失败:', error);
      throw error;
    }
  },

  // 为组合包卡券创建使用记录
  createCouponUsageRecords: async function(couponId, userId, scenicList) {
    try {
      console.log('开始为卡券创建使用记录:', couponId, userId, scenicList.length);

      // 提取所有景区ID
      const scenicIds = scenicList.map(scenic => scenic.id);

      // 调用后端API创建使用记录
      const requestData = {
        couponId: couponId,
        userId: userId,
        scenicIds: scenicIds
      };

      console.log('创建使用记录请求数据:', requestData);

      // 直接调用 HTTP 服务，避免模块加载问题
      const httpService = require('../../utils/httpService');
      const result = await httpService.post('/api/coupon-usage-records/batch/bundle', requestData, {
        loadingText: '创建使用记录中...'
      });
      console.log('使用记录创建成功:', result);

      return result;
    } catch (error) {
      console.error('创建卡券使用记录失败:', error);
      throw error;
    }
  },

  // 为单品创建产品和门票
  createSingleProductAndCoupons: async function(order) {
    try {
      console.log('开始为单品创建产品和门票');

      const productData = {
        name: this.data.productInfo.title,
        description: this.data.productInfo.description,
        price: this.data.productInfo.price,
        scenicId: this.data.scenicId,
        orderId: order.id || order.orderId,
        bundleId: this.data.bundleId || null, // 添加套餐ID支持
        productType: 'single',
        status: 1,
        requireActivation: true,
        validityHours: 24,
        image: this.data.productInfo.image
      };

      console.log('创建单品产品:', productData);
      const product = await apiService.createProduct(productData);
      console.log('单品产品创建成功:', product);

      // 创建门票
      const couponOrderData = {
        orderId: order.id || order.orderId,
        productId: product.id || product.productId,
        userId: this.data.userId,
        productName: this.data.productInfo.title,
        value: this.data.productInfo.price
      };

      console.log('批量创建单品门票:', couponOrderData, this.data.quantity);
      const coupons = await apiService.createMultipleCoupons(couponOrderData, this.data.quantity);
      console.log('单品门票创建成功:', coupons);

    } catch (error) {
      console.error('创建单品产品和门票失败:', error);
      throw error;
    }
  },

  // 显示查看更多景区弹窗
  onShowMoreScenic: function() {
    this.setData({
      showScenicModal: true
    });
  },

  // 关闭景区弹窗
  onCloseScenicModal: function() {
    this.setData({
      showScenicModal: false
    });
  },

  // 点击弹窗背景关闭
  onModalBackgroundTap: function() {
    this.setData({
      showScenicModal: false
    });
  },

  // 阻止弹窗内容点击事件冒泡
  onModalContentTap: function() {
    // 阻止事件冒泡，防止点击内容区域时关闭弹窗
  },

  // 处理推荐员订单
  handleDistributorOrder: async function(order) {
    try {
      console.log('检测推荐员信息...');

      // 检查是否有推荐员信息
      const distributorInfo = distributorUtil.getDistributorInfo();
      if (!distributorInfo) {
        console.log('未检测到推荐员信息，跳过推荐订单创建');
        return;
      }

      // 检查推荐员信息是否有效（24小时内）
      // if (!distributorUtil.isDistributorInfoValid(24)) {
      //   console.log('推荐员信息已过期，跳过推荐订单创建');
      //   return;
      // }

      console.log('检测到有效的推荐员信息:', distributorInfo);

      // 获取推荐员详情（包含推荐员ID和佣金比例）
      const distributorDetail = await this.getDistributorDetailByCode(distributorInfo);
      if (!distributorDetail) {
        console.warn('未找到推荐员详情，跳过推荐订单创建');
        return;
      }

      console.log('推荐员详情:', distributorDetail);

      // 创建推荐订单
      const distributionOrderData = {
        distributorId: distributorDetail.id,
        orderId: order.id || order.orderId,
        orderAmount: this.data.totalAmount,
        commissionRate: distributorDetail.commissionRate, // 默认10%佣金
        baseRate: distributorDetail.commissionRate 
      };

      console.log('创建推荐订单:', distributionOrderData);
      const distributionOrder = await this.createDistributionOrder(distributionOrderData);
      console.log('推荐订单创建成功:', distributionOrder);

      // 显示推荐成功提示
      wx.showToast({
        title: '推荐订单已创建',
        icon: 'success',
        duration: 1500
      });

    } catch (error) {
      console.error('处理推荐员订单失败:', error);
      // 推荐订单创建失败不影响主流程，只记录错误
      console.warn('推荐订单创建失败，但不影响主订单流程');
    }
  },

  // 根据推荐员编号获取推荐员详情
  getDistributorDetailByCode: async function(distributorCode) {
    try {
      console.log('根据编号获取推荐员详情:', distributorCode);

      const app = getApp();
      const baseUrl = app.globalData.baseUrl;

      return new Promise((resolve, reject) => {
        wx.request({
          url: `${baseUrl}/api/distributor/code/${distributorCode}`,
          method: 'GET',
          header: {
            'Content-Type': 'application/json'
          },
          success: (res) => {
            console.log('推荐员详情API响应:', res);

            if (res.statusCode === 200 && res.data.code === 200) {
              resolve(res.data.data);
            } else {
              console.error('获取推荐员详情失败:', res.data);
              resolve(null);
            }
          },
          fail: (error) => {
            console.error('获取推荐员详情请求失败:', error);
            resolve(null);
          }
        });
      });
    } catch (error) {
      console.error('获取推荐员详情异常:', error);
      return null;
    }
  },

  // 创建推荐订单
  createDistributionOrder: async function(distributionOrderData) {
    try {
      console.log('调用推荐订单创建API:', distributionOrderData);

      const app = getApp();
      const baseUrl = app.globalData.baseUrl;

      return new Promise((resolve, reject) => {
        wx.request({
          url: `${baseUrl}/api/distribution/order/create`,
          method: 'POST',
          header: {
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          data: distributionOrderData,
          success: (res) => {
            console.log('推荐订单创建API响应:', res);

            if (res.statusCode === 200 && res.data.code === 200) {
              resolve(res.data.data);
            } else {
              console.error('推荐订单创建失败:', res.data);
              reject(new Error(res.data.message || '推荐订单创建失败'));
            }
          },
          fail: (error) => {
            console.error('推荐订单创建请求失败:', error);
            reject(error);
          }
        });
      });
    } catch (error) {
      console.error('推荐订单创建异常:', error);
      throw error;
    }
  }
});
