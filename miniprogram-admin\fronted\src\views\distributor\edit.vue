<template>
  <div class="distributor-edit">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-button @click="goBack" type="info" plain>
        <el-icon><ArrowLeft /></el-icon>
        返回详情
      </el-button>
      <h2>编辑分销员</h2>
    </div>

    <div v-loading="loading" class="edit-content">
      <el-card>
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="120px"
          class="edit-form"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="分销员ID">
                <el-input v-model="form.id" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="分销员编号">
                <el-input v-model="form.distributorCode" disabled />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="绑定用户ID">
                <el-input v-model="form.userId" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="状态" prop="status">
                <el-select v-model="form.status" style="width: 100%">
                  <el-option label="活跃" value="active" />
                  <el-option label="非活跃" value="inactive" />
                  <el-option label="冻结" value="frozen" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="佣金比例" prop="commissionRate">
                <el-input
                  v-model.number="form.commissionRate"
                  type="number"
                  :min="0"
                  :max="100"
                  :step="0.01"
                >
                  <template #append>%</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="使用自定义比例">
                <el-switch
                  v-model="form.customRate"
                  active-text="是"
                  inactive-text="否"
                  @change="handleCustomRateChange"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="今日激活人数">
                <el-input-number
                  v-model="form.todayActivated"
                  :min="0"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="专属二维码URL">
                <el-input v-model="form.qrcodeUrl" placeholder="系统自动生成" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider content-position="left">佣金信息</el-divider>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="累积佣金总额">
                <el-input
                  v-model.number="form.totalCommission"
                  type="number"
                  :step="0.01"
                >
                  <template #prepend>¥</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="可提现金额">
                <el-input
                  v-model.number="form.availableCommission"
                  type="number"
                  :step="0.01"
                >
                  <template #prepend>¥</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="已提现金额">
                <el-input
                  v-model.number="form.withdrawnCommission"
                  type="number"
                  :step="0.01"
                >
                  <template #prepend>¥</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="未结算金额">
                <el-input
                  v-model.number="form.unsettledCommission"
                  type="number"
                  :step="0.01"
                >
                  <template #prepend>¥</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider content-position="left">时间信息</el-divider>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="创建时间">
                <el-input :value="formatDate(form.createdAt)" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="更新时间">
                <el-input :value="formatDate(form.updatedAt)" disabled />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item>
            <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
              <el-icon><Check /></el-icon>
              保存修改
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
            <el-button @click="goBack">
              <el-icon><Close /></el-icon>
              取消
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 自定义比例设置弹窗 -->
    <el-dialog
      v-model="customRateDialogVisible"
      title="设置自定义佣金比例"
      width="80%"
      :close-on-click-modal="false"
    >
      <div v-loading="customRateLoading" class="custom-rate-content">
        <!-- 讲解产品比例设置 -->
        <el-card class="rate-section">
          <template #header>
            <div class="section-header">
              <h3>讲解产品佣金比例</h3>
              <el-button type="primary" size="small" @click="addProductRate">
                <el-icon><Plus /></el-icon>
                添加产品
              </el-button>
            </div>
          </template>

          <el-table :data="productRates" style="width: 100%" empty-text="暂无设置">
            <el-table-column prop="productId" label="产品ID" width="100" />
            <el-table-column prop="productTitle" label="产品名称" min-width="200" />
            <el-table-column prop="scenicName" label="景区" min-width="150" />
            <el-table-column label="佣金比例" width="150">
              <template #default="{ row, $index }">
                <el-input-number
                  v-model="row.commissionRate"
                  :min="0"
                  :max="100"
                  :precision="2"
                  :step="0.01"
                  size="small"
                  style="width: 120px"
                />
                <span style="margin-left: 5px">%</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="{ row, $index }">
                <el-button
                  type="danger"
                  size="small"
                  @click="removeProductRate($index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 组合包比例设置 -->
        <el-card class="rate-section">
          <template #header>
            <div class="section-header">
              <h3>组合包佣金比例</h3>
              <el-button type="primary" size="small" @click="addBundleRate">
                <el-icon><Plus /></el-icon>
                添加组合包
              </el-button>
            </div>
          </template>

          <el-table :data="bundleRates" style="width: 100%" empty-text="暂无设置">
            <el-table-column prop="bundleId" label="组合包ID" width="100" />
            <el-table-column prop="bundleName" label="组合包名称" min-width="200" />
            <el-table-column prop="scenicNames" label="包含景区" min-width="200" />
            <el-table-column label="佣金比例" width="150">
              <template #default="{ row, $index }">
                <el-input-number
                  v-model="row.commissionRate"
                  :min="0"
                  :max="100"
                  :precision="2"
                  :step="0.01"
                  size="small"
                  style="width: 120px"
                />
                <span style="margin-left: 5px">%</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="{ row, $index }">
                <el-button
                  type="danger"
                  size="small"
                  @click="removeBundleRate($index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="customRateDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveCustomRates" :loading="saveRateLoading">
            保存设置
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 选择产品弹窗 -->
    <el-dialog
      v-model="productSelectDialogVisible"
      title="选择讲解产品"
      width="60%"
    >
      <div class="product-select-content">
        <el-form :inline="true" class="search-form">
          <el-form-item label="产品名称">
            <el-input
              v-model="productSearchForm.title"
              placeholder="请输入产品名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="景区">
            <el-input
              v-model="productSearchForm.scenicId"
              placeholder="请输入景区ID"
              clearable
              style="width: 150px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchProducts">搜索</el-button>
            <el-button @click="resetProductSearch">重置</el-button>
          </el-form-item>
        </el-form>

        <el-table
          :data="productList"
          v-loading="productListLoading"
          @selection-change="handleProductSelectionChange"
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="productId" label="产品ID" width="100" />
          <el-table-column prop="title" label="产品名称" min-width="200" />
          <el-table-column label="景区" min-width="150">
            <template #default="{ row }">
              {{ getScenicNameById(row.scenicId) }}
            </template>
          </el-table-column>
          <el-table-column prop="price" label="价格" width="100">
            <template #default="{ row }">
              ¥{{ row.price }}
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          v-model:current-page="productPagination.current"
          v-model:page-size="productPagination.size"
          :total="productPagination.total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchProducts"
          @current-change="fetchProducts"
          style="margin-top: 20px"
        />
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="productSelectDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmProductSelection">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 选择组合包弹窗 -->
    <el-dialog
      v-model="bundleSelectDialogVisible"
      title="选择组合包"
      width="60%"
    >
      <div class="bundle-select-content">
        <el-form :inline="true" class="search-form">
          <el-form-item label="组合包名称">
            <el-input
              v-model="bundleSearchForm.name"
              placeholder="请输入组合包名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchBundles">搜索</el-button>
            <el-button @click="resetBundleSearch">重置</el-button>
          </el-form-item>
        </el-form>

        <el-table
          :data="bundleList"
          v-loading="bundleListLoading"
          @selection-change="handleBundleSelectionChange"
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="id" label="组合包ID" width="100" />
          <el-table-column prop="name" label="组合包名称" min-width="200" />
          <el-table-column label="包含景区" min-width="200">
            <template #default="{ row }">
              {{ getBundleScenicNames(row.scenicIds) }}
            </template>
          </el-table-column>
          <el-table-column label="优惠金额" width="100">
            <template #default="{ row }">
              ¥{{ row.discount || 0 }}
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          v-model:current-page="bundlePagination.current"
          v-model:page-size="bundlePagination.size"
          :total="bundlePagination.total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchBundles"
          @current-change="fetchBundles"
          style="margin-top: 20px"
        />
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="bundleSelectDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmBundleSelection">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getDistributorDetail, updateDistributor, setProductCommissionRate, setBundleCommissionRate } from '@/api/distributor'
import { getGuideProductPage } from '@/api/guideProduct'
import { getProductBundlePage } from '@/api/productBundle'
import { getScenicPage } from '@/api/scenic'
import { formatDate } from '@/utils'

const router = useRouter()
const route = useRoute()

const distributorId = route.params.id
const loading = ref(false)
const submitLoading = ref(false)
const formRef = ref()

// 表单数据
const form = reactive({
  id: '',
  distributorCode: '',
  userId: '',
  status: 'active',
  commissionRate: 10.00,
  customRate: false,
  todayActivated: 0,
  totalCommission: 0.00,
  availableCommission: 0.00,
  withdrawnCommission: 0.00,
  unsettledCommission: 0.00,
  qrcodeUrl: '',
  createdAt: '',
  updatedAt: ''
})

// 原始数据备份
const originalForm = reactive({})

// 自定义比例相关数据
const customRateDialogVisible = ref(false)
const customRateLoading = ref(false)
const saveRateLoading = ref(false)

// 产品比例数据
const productRates = ref([])
const productSelectDialogVisible = ref(false)
const productListLoading = ref(false)
const productList = ref([])
const selectedProducts = ref([])
const productSearchForm = reactive({
  title: '',
  scenicId: ''
})
const productPagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 组合包比例数据
const bundleRates = ref([])
const bundleSelectDialogVisible = ref(false)
const bundleListLoading = ref(false)
const bundleList = ref([])
const selectedBundles = ref([])
const bundleSearchForm = reactive({
  name: ''
})
const bundlePagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 景区数据
const scenicList = ref([])
const scenicLoading = ref(false)

// 表单验证规则
const rules = {
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  commissionRate: [
    { required: true, message: '请输入佣金比例', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '佣金比例必须在0-100之间', trigger: 'blur' }
  ]
}

// 获取分销员详情
const fetchDistributorDetail = async () => {
  loading.value = true
  try {
    const response = await getDistributorDetail(distributorId)
    if (response.code === 200) {
      const data = response.data
      
      // 填充表单数据
      Object.keys(form).forEach(key => {
        if (data.hasOwnProperty(key)) {
          form[key] = data[key]
        }
      })
      
      // 备份原始数据
      Object.assign(originalForm, form)
    }
  } catch (error) {
    console.error('获取分销员详情失败:', error)
    ElMessage.error('获取分销员详情失败')
  } finally {
    loading.value = false
  }
}

// 返回详情页
const goBack = () => {
  router.push(`/distributor/detail/${distributorId}`)
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    submitLoading.value = true
    
    // 准备提交数据，包含所有必要字段以通过后端验证
    const updateData = {
      // 必须包含的字段以通过验证
      distributorCode: form.distributorCode,
      userId: form.userId,
      status: form.status,
      commissionRate: form.commissionRate,
      customRate: form.customRate,
      // 其他可选字段
      todayActivated: form.todayActivated,
      totalCommission: form.totalCommission,
      availableCommission: form.availableCommission,
      withdrawnCommission: form.withdrawnCommission,
      unsettledCommission: form.unsettledCommission,
      qrcodeUrl: form.qrcodeUrl
    }

    // 检查是否有实际变化
    const hasChanges = Object.keys(form).some(key => {
      if (['id', 'createdAt', 'updatedAt'].includes(key)) {
        return false
      }
      return form[key] !== originalForm[key]
    })

    if (!hasChanges) {
      ElMessage.info('没有数据需要更新')
      return
    }
    
    const response = await updateDistributor(distributorId, updateData)
    if (response.code === 200) {
      ElMessage.success('分销员信息更新成功')
      // 更新原始数据
      Object.assign(originalForm, form)
      // 返回详情页
      setTimeout(() => {
        goBack()
      }, 1000)
    }
  } catch (error) {
    console.error('更新分销员信息失败:', error)
    ElMessage.error(error.response?.data?.message || '更新分销员信息失败')
  } finally {
    submitLoading.value = false
  }
}

// 重置表单
const handleReset = () => {
  Object.assign(form, originalForm)
  formRef.value?.clearValidate()
}

// 自定义比例开关变化处理
const handleCustomRateChange = (value) => {
  if (value) {
    // 打开自定义比例设置弹窗
    customRateDialogVisible.value = true
    loadExistingRates()
  }
}

// 加载现有的自定义比例设置
const loadExistingRates = async () => {
  customRateLoading.value = true
  try {
    // 这里可以调用API获取现有的自定义比例设置
    // 暂时使用空数据
    productRates.value = []
    bundleRates.value = []
  } catch (error) {
    console.error('加载现有比例设置失败:', error)
    ElMessage.error('加载现有比例设置失败')
  } finally {
    customRateLoading.value = false
  }
}

// 添加产品比例
const addProductRate = () => {
  productSelectDialogVisible.value = true
  fetchProducts()
}

// 添加组合包比例
const addBundleRate = () => {
  bundleSelectDialogVisible.value = true
  fetchBundles()
}

// 获取景区列表
const fetchScenicList = async () => {
  if (scenicList.value.length > 0) return // 避免重复加载

  scenicLoading.value = true
  try {
    const { data } = await getScenicPage({
      current: 1,
      size: 1000 // 获取所有景区用于映射
    })
    scenicList.value = data.records || []
  } catch (error) {
    console.error('获取景区列表失败:', error)
    ElMessage.error('获取景区列表失败')
  } finally {
    scenicLoading.value = false
  }
}

// 根据景区ID获取景区名称
const getScenicNameById = (scenicId) => {
  if (!scenicId) return '未知景区'
  const scenic = scenicList.value.find(s => s.scenicId === scenicId || s.id === scenicId)
  return scenic ? scenic.title : `景区ID: ${scenicId}`
}

// 根据景区ID数组获取景区名称列表
const getBundleScenicNames = (scenicIds) => {
  if (!scenicIds || !Array.isArray(scenicIds)) return '无景区信息'

  const names = scenicIds.map(id => getScenicNameById(id)).filter(name => name !== '未知景区')
  return names.length > 0 ? names.join(', ') : '无有效景区'
}

// 获取产品列表
const fetchProducts = async () => {
  productListLoading.value = true
  try {
    // 确保景区数据已加载
    await fetchScenicList()

    const params = {
      current: productPagination.current,
      size: productPagination.size,
      ...productSearchForm
    }

    // 过滤空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })

    const { data } = await getGuideProductPage(params)
    productList.value = data.records || []
    productPagination.total = data.total || 0
  } catch (error) {
    console.error('获取产品列表失败:', error)
    ElMessage.error('获取产品列表失败')
  } finally {
    productListLoading.value = false
  }
}

// 获取组合包列表
const fetchBundles = async () => {
  bundleListLoading.value = true
  try {
    // 确保景区数据已加载
    await fetchScenicList()

    const params = {
      current: bundlePagination.current,
      size: bundlePagination.size,
      ...bundleSearchForm
    }

    // 过滤空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })

    const { data } = await getProductBundlePage(params)
    bundleList.value = data.records || []
    bundlePagination.total = data.total || 0
  } catch (error) {
    console.error('获取组合包列表失败:', error)
    ElMessage.error('获取组合包列表失败')
  } finally {
    bundleListLoading.value = false
  }
}

// 搜索产品
const searchProducts = () => {
  productPagination.current = 1
  fetchProducts()
}

// 重置产品搜索
const resetProductSearch = () => {
  Object.keys(productSearchForm).forEach(key => {
    productSearchForm[key] = ''
  })
  productPagination.current = 1
  fetchProducts()
}

// 搜索组合包
const searchBundles = () => {
  bundlePagination.current = 1
  fetchBundles()
}

// 重置组合包搜索
const resetBundleSearch = () => {
  Object.keys(bundleSearchForm).forEach(key => {
    bundleSearchForm[key] = ''
  })
  bundlePagination.current = 1
  fetchBundles()
}

// 产品选择变化
const handleProductSelectionChange = (selection) => {
  selectedProducts.value = selection
}

// 组合包选择变化
const handleBundleSelectionChange = (selection) => {
  selectedBundles.value = selection
}

// 确认产品选择
const confirmProductSelection = () => {
  selectedProducts.value.forEach(product => {
    // 检查是否已存在
    const exists = productRates.value.some(rate => rate.productId === product.productId)
    if (!exists) {
      productRates.value.push({
        productId: product.productId,
        productTitle: product.title,
        scenicName: getScenicNameById(product.scenicId),
        commissionRate: form.commissionRate || 10.00
      })
    }
  })
  productSelectDialogVisible.value = false
  selectedProducts.value = []
}

// 确认组合包选择
const confirmBundleSelection = () => {
  selectedBundles.value.forEach(bundle => {
    // 检查是否已存在
    const exists = bundleRates.value.some(rate => rate.bundleId === bundle.id)
    if (!exists) {
      bundleRates.value.push({
        bundleId: bundle.id,
        bundleName: bundle.name,
        scenicNames: getBundleScenicNames(bundle.scenicIds),
        commissionRate: form.commissionRate || 10.00
      })
    }
  })
  bundleSelectDialogVisible.value = false
  selectedBundles.value = []
}

// 删除产品比例
const removeProductRate = (index) => {
  productRates.value.splice(index, 1)
}

// 删除组合包比例
const removeBundleRate = (index) => {
  bundleRates.value.splice(index, 1)
}

// 保存自定义比例设置
const saveCustomRates = async () => {
  saveRateLoading.value = true
  try {
    // 保存产品比例
    for (const productRate of productRates.value) {
      await setProductCommissionRate({
        distributorId: parseInt(distributorId),
        productId: parseInt(productRate.productId),
        commissionRate: productRate.commissionRate
      })
    }

    // 保存组合包比例
    for (const bundleRate of bundleRates.value) {
      await setBundleCommissionRate({
        distributorId: parseInt(distributorId),
        bundleId: parseInt(bundleRate.bundleId),
        commissionRate: bundleRate.commissionRate
      })
    }

    ElMessage.success('自定义比例设置保存成功')
    customRateDialogVisible.value = false
  } catch (error) {
    console.error('保存自定义比例设置失败:', error)
    ElMessage.error('保存自定义比例设置失败')
  } finally {
    saveRateLoading.value = false
  }
}

onMounted(() => {
  fetchDistributorDetail()
})
</script>

<style lang="scss" scoped>
.distributor-edit {
  .page-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      color: #303133;
    }
  }
  
  .edit-content {
    .edit-form {
      max-width: 800px;
      
      .el-divider {
        margin: 30px 0 20px 0;
        
        :deep(.el-divider__text) {
          font-weight: 500;
          color: #409EFF;
        }
      }
      
      .el-form-item {
        margin-bottom: 20px;
      }
      
      .el-input-number {
        width: 100%;
      }
    }
  }

  // 自定义比例设置样式
  .custom-rate-content {
    .rate-section {
      margin-bottom: 20px;

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        h3 {
          margin: 0;
          color: #303133;
          font-size: 16px;
        }
      }
    }
  }

  .product-select-content,
  .bundle-select-content {
    .search-form {
      margin-bottom: 20px;
      padding: 20px;
      background-color: #f5f7fa;
      border-radius: 4px;
    }
  }

  .dialog-footer {
    .el-button {
      margin-left: 10px;
    }
  }
}

@media (max-width: 768px) {
  .distributor-edit {
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }
    
    .edit-form {
      .el-row {
        .el-col {
          margin-bottom: 10px;
        }
      }
    }
  }
}
</style>
