<template>
  <div class="review-form">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑评价' : '新增评价' }}</h2>
      <el-button @click="handleBack">返回</el-button>
    </div>

    <el-card>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        style="max-width: 600px"
      >
        <el-form-item label="用户ID" prop="userId">
          <el-select
            v-model="form.userId"
            placeholder="请选择用户"
            style="width: 100%"
            filterable
            clearable
            :loading="userLoading"
          >
            <el-option
              v-for="user in userList"
              :key="user.id"
              :label="`${user.nickname} (ID: ${user.id})`"
              :value="user.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="景区ID" prop="scenicId">
          <el-select
            v-model="form.scenicId"
            placeholder="请选择景区"
            style="width: 100%"
            filterable
            clearable
            :loading="scenicLoading"
          >
            <el-option
              v-for="scenic in scenicList"
              :key="scenic.id"
              :label="`${scenic.title} (ID: ${scenic.id})`"
              :value="scenic.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="评价内容" prop="content">
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="4"
            placeholder="请输入评价内容"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="评价时间" prop="reviewTime">
          <el-date-picker
            v-model="form.reviewTime"
            type="datetime"
            placeholder="请选择评价时间"
            style="width: 100%"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">显示</el-radio>
            <el-radio :label="0">隐藏</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="loading">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="handleBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getReviewById, createReview, updateReview } from '@/api/review'
import { getUserList } from '@/api/user'
import { getScenicPage } from '@/api/scenic'

const router = useRouter()
const route = useRoute()

// 计算属性
const isEdit = computed(() => !!route.params.id)

// 响应式数据
const loading = ref(false)
const userLoading = ref(false)
const scenicLoading = ref(false)
const formRef = ref()
const userList = ref([])
const scenicList = ref([])

const form = reactive({
  userId: null,
  scenicId: null,
  content: '',
  reviewTime: '',
  status: 1
})

// 表单验证规则
const rules = {
  userId: [
    { required: true, message: '请选择用户', trigger: 'change' }
  ],
  scenicId: [
    { required: true, message: '请选择景区', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入评价内容', trigger: 'blur' },
    { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 获取用户列表
const fetchUserList = async () => {
  try {
    userLoading.value = true
    const { data } = await getUserList({ current: 1, size: 1000 })
    userList.value = data.records || []
  } catch (error) {
    ElMessage.error('获取用户列表失败')
    console.error(error)
  } finally {
    userLoading.value = false
  }
}

// 获取景区列表
const fetchScenicList = async () => {
  try {
    scenicLoading.value = true
    const { data } = await getScenicPage({
      current: 1,
      size: 1000
    })
    scenicList.value = data.records || []
  } catch (error) {
    ElMessage.error('获取景区列表失败')
    console.error(error)
  } finally {
    scenicLoading.value = false
  }
}

// 获取详情数据
const fetchDetail = async () => {
  if (!isEdit.value) return

  try {
    const { data } = await getReviewById(route.params.id)
    Object.assign(form, data)
  } catch (error) {
    ElMessage.error('获取评价详情失败')
    console.error(error)
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    if (isEdit.value) {
      await updateReview(route.params.id, form)
      ElMessage.success('更新成功')
    } else {
      await createReview(form)
      ElMessage.success('创建成功')
    }

    handleBack()
  } catch (error) {
    if (error !== false) {
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
      console.error(error)
    }
  } finally {
    loading.value = false
  }
}

// 重置表单
const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  if (isEdit.value) {
    fetchDetail()
  }
}

// 返回列表
const handleBack = () => {
  router.push('/review/list')
}

// 初始化
onMounted(() => {
  fetchUserList()
  fetchScenicList()
  if (isEdit.value) {
    fetchDetail()
  }
})
</script>

<style scoped>
.review-form {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}
</style>
