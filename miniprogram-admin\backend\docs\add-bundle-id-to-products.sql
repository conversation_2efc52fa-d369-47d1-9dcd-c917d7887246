-- 为products表添加bundle_id字段
-- 执行时间：2025-01-06
-- 说明：添加bundle_id字段用于关联套餐产品

-- 添加bundle_id字段
ALTER TABLE `products` ADD COLUMN `bundle_id` BIGINT NULL COMMENT '关联套餐产品ID，当产品为套餐子产品时指向对应的套餐产品ID' AFTER `order_id`;

-- 添加索引以优化查询性能
CREATE INDEX `idx_products_bundle_id` ON `products` (`bundle_id`);

-- 验证字段是否添加成功
-- SELECT * FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = 'tourism_miniprogram' AND TABLE_NAME = 'products' AND COLUMN_NAME = 'bundle_id';
